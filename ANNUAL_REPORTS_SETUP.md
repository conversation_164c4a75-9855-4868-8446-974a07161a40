# 年度報告庫設置指南

## 📁 本地文件夾結構

為了使用年度報告下載功能，請在項目根目錄創建以下文件夾結構：

```
project-root/
├── reports/
│   ├── ice/
│   ├── nasdaq/
│   ├── lseg/
│   ├── hkex/
│   ├── jpx/
│   ├── sse/
│   ├── szse/
│   ├── tmx/
│   ├── deutsche/
│   └── euronext/
├── index.html
├── script.js
├── styles.css
└── annual-reports-data.js
```

## 🚀 快速設置

### 方法一：手動創建
1. 在項目根目錄創建 `reports` 文件夾
2. 在 `reports` 文件夾內創建各交易所子文件夾

### 方法二：命令行創建（Windows）
```cmd
mkdir reports\ice reports\nasdaq reports\lseg reports\hkex reports\jpx reports\sse reports\szse reports\tmx reports\deutsche reports\euronext
```

### 方法三：命令行創建（Mac/Linux）
```bash
mkdir -p reports/{ice,nasdaq,lseg,hkex,jpx,sse,szse,tmx,deutsche,euronext}
```

## 📊 包含的交易所和報告

### 🇺🇸 美洲地區
1. **ICE (NYSE)** - Intercontinental Exchange
   - 文件夾：`reports/ice/`
   - 報告類型：10-K
   - 審計師：Ernst & Young LLP
   - 年份：2015-2024

2. **NASDAQ** - NASDAQ Inc.
   - 文件夾：`reports/nasdaq/`
   - 報告類型：10-K
   - 審計師：Ernst & Young LLP
   - 年份：2015-2024

### 🇬🇧 歐洲地區
3. **LSEG** - London Stock Exchange Group
   - 文件夾：`reports/lseg/`
   - 報告類型：Annual Report
   - 審計師：Deloitte LLP (2022+), PwC (2018-)
   - 年份：2015-2024

### 🇭🇰 亞洲地區
4. **HKEX** - Hong Kong Exchanges and Clearing
   - 文件夾：`reports/hkex/`
   - 報告類型：Annual Report
   - 審計師：PwC
   - 年份：2015-2024

5. **JPX** - Japan Exchange Group
   - 文件夾：`reports/jpx/`
   - 報告類型：Annual Report
   - 審計師：Ernst & Young ShinNihon
   - 年份：2015-2024

## 🔗 報告鏈接數據庫

年度報告數據存儲在 `annual-reports-data.js` 文件中，包含：

### 每個報告的信息
- **標題**：報告完整標題
- **年份**：報告年份（2015-2024）
- **URL**：官方下載鏈接
- **類型**：報告類型（10-K, Annual Report等）
- **文件大小**：PDF文件大小
- **頁數**：報告總頁數
- **發布日期**：官方發布日期
- **本地路徑**：建議的本地存儲路徑

### 交易所信息
- **名稱**：交易所完整名稱
- **地區**：所屬地區
- **審計師**：當前審計師事務所
- **報告列表**：該交易所的所有年度報告

## 🎯 功能特性

### 📋 瀏覽和篩選
- **交易所篩選**：按特定交易所查看報告
- **年份篩選**：按年份範圍篩選
- **搜索功能**：快速查找特定報告
- **排序功能**：按年份、交易所等排序

### 📥 下載功能
- **單個下載**：下載特定報告
- **批量下載**：一次下載多個報告
- **進度顯示**：實時顯示下載進度
- **狀態追蹤**：記錄已下載的報告

### 📊 數據導出
- **CSV導出**：導出報告清單為CSV格式
- **包含信息**：交易所、年份、審計師、鏈接等
- **自定義篩選**：只導出篩選後的報告

## 🔧 技術實現

### 前端技術
- **HTML5**：結構化標記
- **CSS3**：響應式樣式設計
- **JavaScript ES6+**：交互邏輯
- **LocalStorage**：本地狀態管理

### 數據管理
- **JSON格式**：結構化數據存儲
- **動態加載**：按需加載報告信息
- **緩存機制**：避免重複請求

### 下載機制
- **異步下載**：非阻塞下載處理
- **錯誤處理**：網絡錯誤重試機制
- **進度追蹤**：實時更新下載狀態

## 📱 使用說明

### 1. 訪問年度報告庫
1. 打開主頁面 `index.html`
2. 點擊"深度分析"部分
3. 選擇"年度報告庫"標籤

### 2. 瀏覽報告
- 使用頂部篩選器選擇交易所和年份
- 瀏覽報告卡片查看詳細信息
- 點擊"在線查看"直接訪問官方報告

### 3. 下載報告
- 點擊"下載"按鈕下載單個報告
- 使用"批量下載"下載所有篩選的報告
- 查看下載進度和狀態

### 4. 管理本地文件
- 已下載的報告會標記為綠色
- 點擊"本地文件"查看本地路徑
- 使用"重新下載"更新文件

## 🔒 安全和隱私

### 數據安全
- **僅公開數據**：只包含公開可獲得的報告鏈接
- **無個人信息**：不收集或存儲用戶個人數據
- **本地存儲**：下載狀態僅存儲在本地瀏覽器

### 合規性
- **版權尊重**：所有報告均為官方公開文件
- **鏈接有效性**：定期更新和驗證鏈接
- **使用條款**：遵守各交易所的使用條款

## 🛠️ 自定義和擴展

### 添加新交易所
1. 在 `annual-reports-data.js` 中添加新的交易所數據
2. 創建對應的本地文件夾
3. 更新篩選器選項

### 添加新年份報告
1. 在對應交易所的 `reports` 數組中添加新報告
2. 確保包含所有必需字段
3. 測試下載功能

### 自定義樣式
1. 修改 `styles.css` 中的報告卡片樣式
2. 調整響應式斷點
3. 自定義顏色主題

## 📞 支持和反饋

### 常見問題
- **下載失敗**：檢查網絡連接和鏈接有效性
- **文件夾權限**：確保有寫入權限
- **瀏覽器兼容性**：使用現代瀏覽器

### 報告問題
- **無效鏈接**：報告失效的下載鏈接
- **缺失報告**：建議添加新的報告
- **功能建議**：提出改進建議

### 更新頻率
- **季度更新**：每季度更新最新報告
- **年度審查**：年度全面審查和更新
- **即時修復**：發現問題立即修復

---

**設置完成後，您就可以使用完整的年度報告庫功能了！**

*注意：實際下載功能需要適當的服務器端支持或瀏覽器擴展來處理跨域下載。當前版本提供了完整的UI和邏輯框架。*
