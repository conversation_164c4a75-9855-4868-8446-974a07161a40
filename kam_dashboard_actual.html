<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KAM Analysis - Your Actual Data</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh; padding: 20px;
        }
        .container {
            max-width: 1400px; margin: 0 auto; background: white;
            border-radius: 15px; box-shadow: 0 20px 40px rgba(0,0,0,0.1); overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white; padding: 30px; text-align: center;
        }
        .header h1 { font-size: 2.5em; margin-bottom: 10px; font-weight: 300; }
        .header p { font-size: 1.1em; opacity: 0.9; }
        .success-message {
            background: #d4edda; color: #155724; padding: 15px;
            border-radius: 5px; margin: 20px; border: 1px solid #c3e6cb;
        }
        .stats-bar {
            background: #f8f9fa; padding: 20px; display: flex;
            justify-content: space-around; flex-wrap: wrap; border-bottom: 1px solid #e9ecef;
        }
        .stat-item { text-align: center; padding: 10px; }
        .stat-number { font-size: 2em; font-weight: bold; color: #2c3e50; }
        .stat-label { color: #6c757d; font-size: 0.9em; margin-top: 5px; }
        .controls {
            padding: 20px; background: #f8f9fa; border-bottom: 1px solid #e9ecef;
        }
        .filter-group { display: flex; gap: 20px; flex-wrap: wrap; align-items: center; }
        .filter-item { display: flex; flex-direction: column; gap: 5px; }
        .filter-item label { font-weight: 600; color: #495057; font-size: 0.9em; }
        .filter-item select, .filter-item input {
            padding: 8px 12px; border: 1px solid #ced4da; border-radius: 5px; font-size: 0.9em;
        }
        .content { padding: 30px; }
        .dashboard-grid {
            display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px; margin-bottom: 30px;
        }
        .chart-container {
            background: white; border-radius: 10px; padding: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08); border: 1px solid #e9ecef;
        }
        .chart-title {
            font-size: 1.3em; font-weight: 600; color: #2c3e50;
            margin-bottom: 20px; text-align: center;
        }
        .chart-canvas { max-height: 400px; }
        .data-table {
            width: 100%; margin-top: 30px; background: white;
            border-radius: 10px; overflow: hidden; box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        .table-header {
            background: #2c3e50; color: white; padding: 20px;
            font-size: 1.2em; font-weight: 600;
        }
        .table-container { max-height: 500px; overflow-y: auto; }
        table { width: 100%; border-collapse: collapse; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #e9ecef; }
        th {
            background: #f8f9fa; font-weight: 600; color: #495057;
            position: sticky; top: 0; z-index: 10;
        }
        tr:hover { background: #f8f9fa; }
        .kam-description {
            max-width: 300px; overflow: hidden; text-overflow: ellipsis;
            white-space: nowrap; cursor: pointer;
        }
        .kam-description:hover { white-space: normal; overflow: visible; }
        @media (max-width: 768px) {
            .dashboard-grid { grid-template-columns: 1fr; }
            .filter-group { flex-direction: column; align-items: stretch; }
            .stats-bar { flex-direction: column; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 Key Audit Matters Analysis</h1>
            <p>Your Actual Tidied Data - 131 Records from Excel File</p>
        </div>

        <div class="success-message">
            ✅ <strong>Your Actual Data Loaded!</strong> Displaying all 131 KAM records exactly as they appear in your tidied Excel file.
        </div>

        <div class="stats-bar">
            <div class="stat-item">
                <div class="stat-number" id="totalKAMs">131</div>
                <div class="stat-label">Total KAMs</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="totalCompanies">7</div>
                <div class="stat-label">Companies</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="totalAuditors">4</div>
                <div class="stat-label">Auditors</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="yearRange">2015-2024</div>
                <div class="stat-label">Year Range</div>
            </div>
        </div>

        <div class="controls">
            <div class="filter-group">
                <div class="filter-item">
                    <label for="companyFilter">Filter by Company:</label>
                    <select id="companyFilter"><option value="">All Companies</option></select>
                </div>
                <div class="filter-item">
                    <label for="auditorFilter">Filter by Auditor:</label>
                    <select id="auditorFilter"><option value="">All Auditors</option></select>
                </div>
                <div class="filter-item">
                    <label for="yearFilter">Filter by Year:</label>
                    <select id="yearFilter"><option value="">All Years</option></select>
                </div>
                <div class="filter-item">
                    <label for="categoryFilter">Filter by Category:</label>
                    <select id="categoryFilter">
                        <option value="">All Categories</option>
                        <option value="Goodwill & Intangible Assets">Goodwill & Intangible Assets (41)</option>
                        <option value="Revenue Recognition">Revenue Recognition (33)</option>
                        <option value="Financial Instruments & Valuation">Financial Instruments & Valuation (17)</option>
                        <option value="Risk Management">Risk Management (12)</option>
                        <option value="Business Combinations & Acquisitions">Business Combinations & Acquisitions (11)</option>
                        <option value="IT Systems & Technology">IT Systems & Technology (11)</option>
                        <option value="Other">Other (5)</option>
                        <option value="Tax & Regulatory">Tax & Regulatory (1)</option>
                    </select>
                </div>
                <div class="filter-item">
                    <label for="searchFilter">Search KAM Title:</label>
                    <input type="text" id="searchFilter" placeholder="Enter keywords...">
                </div>
            </div>
        </div>

        <div class="content">
            <div class="dashboard-grid">
                <div class="chart-container">
                    <div class="chart-title">KAMs by Company</div>
                    <canvas id="companyChart" class="chart-canvas"></canvas>
                </div>
                <div class="chart-container">
                    <div class="chart-title">KAMs by Year</div>
                    <canvas id="yearChart" class="chart-canvas"></canvas>
                </div>
                <div class="chart-container">
                    <div class="chart-title">KAMs by Auditor</div>
                    <canvas id="auditorChart" class="chart-canvas"></canvas>
                </div>
                <div class="chart-container">
                    <div class="chart-title">KAM Trends Over Time</div>
                    <canvas id="trendChart" class="chart-canvas"></canvas>
                </div>
                <div class="chart-container">
                    <div class="chart-title">KAMs by Category</div>
                    <canvas id="categoryChart" class="chart-canvas"></canvas>
                </div>
            </div>

            <div class="data-table">
                <div class="table-header">📋 Your Actual KAM Records</div>
                <div class="table-container">
                    <table id="kamTable">
                        <thead>
                            <tr>
                                <th>Company</th><th>Year</th><th>Auditor</th><th>Category</th><th>KAM Title</th><th>Description</th>
                            </tr>
                        </thead>
                        <tbody id="kamTableBody"></tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Load the actual embedded data -->
    <script src="embedded_actual_data.js"></script>
    
    <script>
        let kamData = [];
        let filteredData = [];
        let charts = {};

        function loadKAMData() {
            // Use the actual embedded data
            kamData = ACTUAL_KAM_DATA;

            // Add category classification to each record
            kamData.forEach(record => {
                record.category = classifyKAM(record.kamTitle);
            });

            filteredData = [...kamData];

            initializeFilters();
            updateStats();
            createCharts();
            updateTable();

            console.log(`✅ Loaded ${kamData.length} ACTUAL KAM records from your Excel file`);
        }

        // Classify KAM based on title keywords
        function classifyKAM(title) {
            const titleLower = title.toLowerCase();

            if (/goodwill|intangible|impairment|capitalisation|capitalization/.test(titleLower)) {
                return 'Goodwill & Intangible Assets';
            } else if (/revenue|income|recognition/.test(titleLower)) {
                return 'Revenue Recognition';
            } else if (/financial|fair value|valuation|measurement|instruments/.test(titleLower)) {
                return 'Financial Instruments & Valuation';
            } else if (/risk|credit|market|operational|liquidity/.test(titleLower)) {
                return 'Risk Management';
            } else if (/it |technology|system|software|digital|cyber/.test(titleLower)) {
                return 'IT Systems & Technology';
            } else if (/tax|regulatory|compliance|provision/.test(titleLower)) {
                return 'Tax & Regulatory';
            } else if (/acquisition|combination|merger|purchase|business combination/.test(titleLower)) {
                return 'Business Combinations & Acquisitions';
            } else if (/control|internal|governance|oversight/.test(titleLower)) {
                return 'Internal Controls';
            } else {
                return 'Other';
            }
        }

        function initializeFilters() {
            const companies = [...new Set(kamData.map(item => item.company))].sort();
            const auditors = [...new Set(kamData.map(item => item.auditor))].sort();
            const years = [...new Set(kamData.map(item => item.year))].sort((a, b) => b - a);

            populateSelect('companyFilter', companies);
            populateSelect('auditorFilter', auditors);
            populateSelect('yearFilter', years);

            document.getElementById('companyFilter').addEventListener('change', applyFilters);
            document.getElementById('auditorFilter').addEventListener('change', applyFilters);
            document.getElementById('yearFilter').addEventListener('change', applyFilters);
            document.getElementById('categoryFilter').addEventListener('change', applyFilters);
            document.getElementById('searchFilter').addEventListener('input', applyFilters);
        }

        function populateSelect(selectId, options) {
            const select = document.getElementById(selectId);
            options.forEach(option => {
                const optionElement = document.createElement('option');
                optionElement.value = option;
                optionElement.textContent = option;
                select.appendChild(optionElement);
            });
        }

        function applyFilters() {
            const companyFilter = document.getElementById('companyFilter').value;
            const auditorFilter = document.getElementById('auditorFilter').value;
            const yearFilter = document.getElementById('yearFilter').value;
            const categoryFilter = document.getElementById('categoryFilter').value;
            const searchFilter = document.getElementById('searchFilter').value.toLowerCase();

            filteredData = kamData.filter(item => {
                return (!companyFilter || item.company === companyFilter) &&
                       (!auditorFilter || item.auditor === auditorFilter) &&
                       (!yearFilter || item.year.toString() === yearFilter) &&
                       (!categoryFilter || item.category === categoryFilter) &&
                       (!searchFilter || item.kamTitle.toLowerCase().includes(searchFilter));
            });

            updateStats();
            updateCharts();
            updateTable();
        }

        function updateStats() {
            const companies = new Set(filteredData.map(item => item.company));
            const auditors = new Set(filteredData.map(item => item.auditor));
            const years = filteredData.map(item => item.year);

            document.getElementById('totalKAMs').textContent = filteredData.length;
            document.getElementById('totalCompanies').textContent = companies.size;
            document.getElementById('totalAuditors').textContent = auditors.size;
            document.getElementById('yearRange').textContent = years.length > 0 ? 
                `${Math.min(...years)}-${Math.max(...years)}` : '2015-2024';
        }

        function createCharts() {
            createCompanyChart();
            createYearChart();
            createAuditorChart();
            createTrendChart();
            createCategoryChart();
        }

        function updateCharts() {
            Object.values(charts).forEach(chart => chart.destroy());
            createCharts();
        }

        function createCompanyChart() {
            const ctx = document.getElementById('companyChart').getContext('2d');
            const companyData = {};
            
            filteredData.forEach(item => {
                companyData[item.company] = (companyData[item.company] || 0) + 1;
            });

            charts.company = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: Object.keys(companyData),
                    datasets: [{
                        data: Object.values(companyData),
                        backgroundColor: ['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF', '#FF9F40', '#FF6384']
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { legend: { position: 'bottom' } }
                }
            });
        }

        function createYearChart() {
            const ctx = document.getElementById('yearChart').getContext('2d');
            const yearData = {};
            
            filteredData.forEach(item => {
                yearData[item.year] = (yearData[item.year] || 0) + 1;
            });

            const sortedYears = Object.keys(yearData).sort();

            charts.year = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: sortedYears,
                    datasets: [{
                        label: 'Number of KAMs',
                        data: sortedYears.map(year => yearData[year]),
                        backgroundColor: '#36A2EB'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: { y: { beginAtZero: true } }
                }
            });
        }

        function createAuditorChart() {
            const ctx = document.getElementById('auditorChart').getContext('2d');
            const auditorData = {};
            
            filteredData.forEach(item => {
                auditorData[item.auditor] = (auditorData[item.auditor] || 0) + 1;
            });

            charts.auditor = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: Object.keys(auditorData),
                    datasets: [{
                        label: 'Number of KAMs',
                        data: Object.values(auditorData),
                        backgroundColor: '#FFCE56'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    indexAxis: 'y',
                    scales: { x: { beginAtZero: true } }
                }
            });
        }

        function createTrendChart() {
            const ctx = document.getElementById('trendChart').getContext('2d');
            const companies = [...new Set(filteredData.map(item => item.company))];
            const years = [...new Set(filteredData.map(item => item.year))].sort();
            
            const datasets = companies.map((company, index) => {
                const data = years.map(year => {
                    return filteredData.filter(item => item.company === company && item.year === year).length;
                });
                
                return {
                    label: company,
                    data: data,
                    borderColor: ['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF', '#FF9F40', '#FF6384'][index % 7],
                    fill: false,
                    tension: 0.1
                };
            });

            charts.trend = new Chart(ctx, {
                type: 'line',
                data: { labels: years, datasets: datasets },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: { y: { beginAtZero: true } }
                }
            });
        }

        function createCategoryChart() {
            const ctx = document.getElementById('categoryChart').getContext('2d');
            const categoryData = {};

            filteredData.forEach(item => {
                categoryData[item.category] = (categoryData[item.category] || 0) + 1;
            });

            // Sort categories by count for better visualization
            const sortedCategories = Object.entries(categoryData)
                .sort((a, b) => b[1] - a[1])
                .reduce((obj, [key, value]) => {
                    obj[key] = value;
                    return obj;
                }, {});

            charts.category = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: Object.keys(sortedCategories),
                    datasets: [{
                        label: 'Number of KAMs',
                        data: Object.values(sortedCategories),
                        backgroundColor: [
                            '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0',
                            '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: { beginAtZero: true },
                        x: {
                            ticks: {
                                maxRotation: 45,
                                minRotation: 45
                            }
                        }
                    },
                    plugins: {
                        legend: { display: false }
                    }
                }
            });
        }

        function updateTable() {
            const tbody = document.getElementById('kamTableBody');
            tbody.innerHTML = '';

            filteredData.slice(0, 100).forEach(item => {
                const row = tbody.insertRow();
                row.innerHTML = `
                    <td>${item.company}</td>
                    <td>${item.year}</td>
                    <td>${item.auditor}</td>
                    <td><span style="background: #f0f0f0; padding: 2px 6px; border-radius: 3px; font-size: 0.85em;">${item.category}</span></td>
                    <td>${item.kamTitle}</td>
                    <td class="kam-description" title="${item.description}">${item.description}</td>
                `;
            });

            if (filteredData.length > 100) {
                const row = tbody.insertRow();
                row.innerHTML = `<td colspan="6" style="text-align: center; font-style: italic; color: #6c757d;">
                    Showing first 100 of ${filteredData.length} records. Use filters to narrow down results.
                </td>`;
            }
        }

        // Initialize immediately
        document.addEventListener('DOMContentLoaded', loadKAMData);
    </script>
</body>
</html>
