{"timestamp": "2025-07-01T01:17:32.323971", "summary": {"total_records": 290, "high_confidence": 208, "medium_confidence": 60, "low_confidence": 22, "high_confidence_percentage": 71.**************, "average_confidence_score": 67.**************}, "common_issues": {"KAM title not found in PDF": 128, "Auditor mismatch: PDF=PwC, Record=Unknown": 51, "Auditor mismatch: PDF=Ernst & Young, Record=Unknown": 28, "No KAM sections identified in PDF": 19, "Low description word overlap with PDF": 15, "Auditor mismatch: PDF=KPMG, Record=Unknown": 14, "Could not identify auditor from PDF": 14, "Auditor mismatch: PDF=Ernst & Young, Record=Independent Registered Public Accounting Firm": 8, "Auditor mismatch: PDF=Deloitte, Record=Unknown": 3, "Auditor mismatch: PDF=Deloitte, Record=Independent Auditor": 3, "Auditor mismatch: PDF=PwC, Record=Independent German public auditor": 2, "KAM title not exact match, similar: ['determination', 'materiality', 'materiality']": 2, "Auditor mismatch: PDF=Ernst & Young, Record=[具体审计师名称]": 2, "Could not extract text from PDF": 2, "KAM title not exact match, similar: ['impairment', 'impairment', 'impairment']": 1, "Auditor mismatch: PDF=KPMG, Record=Independent Auditors": 1, "Auditor mismatch: PDF=PwC, Record=Independent Auditor": 1, "KAM title not exact match, similar: ['misstatements', 'misstatements', 'misstatement']": 1, "KAM title not exact match, similar: ['assessments', 'assessments']": 1, "KAM title not exact match, similar: ['going-concern']": 1, "KAM title not exact match, similar: ['itsacquisition']": 1, "KAM title not exact match, similar: ['untingforacquisitionofelliemae']": 1, "KAM title not exact match, similar: ['untingforincometaxes']": 1, "KAM title not exact match, similar: ['untingforincometaxes', \"'saccountingforconsoli\"]": 1, "KAM title not exact match, similar: ['accountittniigfoffriniicometattxes']": 1, "KAM title not exact match, similar: ['accountinttgforbusineiisscombinatiottns', 'accountinttgforincometaxes']": 1, "KAM title not exact match, similar: ['accountinttgforincometaxes', 'accountinttgforbusineiisscombinatiottns']": 1, "KAM title not exact match, similar: ['accountingforincometaxes']": 1, "Auditor mismatch: PDF=KPMG, Record=NDmna tChartered Professional Accountants, Licensed Public Accountants": 1, "Auditor mismatch: PDF=KPMG, Record=Abhimanyu Verma": 1}, "company_statistics": {"Deutsche Börse AG": {"total": 45, "high_conf": 32, "avg_score": 66.**************}, "Euronext N.V.": {"total": 66, "high_conf": 46, "avg_score": 65.**************}, "HKEX": {"total": 35, "high_conf": 30, "avg_score": 76.**************}, "ICE": {"total": 18, "high_conf": 4, "avg_score": 37.5}, "LSEG": {"total": 94, "high_conf": 81, "avg_score": 74.**************}, "Nasdaq Inc.": {"total": 16, "high_conf": 6, "avg_score": 58.4375}, "TMX Group": {"total": 16, "high_conf": 9, "avg_score": 59.0625}}, "detailed_results": [{"record_id": 0, "company": "Deutsche Börse AG", "year": 2017, "file_name": "Pages from DBG-annual-report-2017.pdf", "issues": ["KAM title not found in PDF", "Auditor mismatch: PDF=KPMG, Record=Unknown"], "confidence_score": 45, "kam_sections_found": 3}, {"record_id": 1, "company": "Deutsche Börse AG", "year": 2017, "file_name": "Pages from DBG-annual-report-2017.pdf", "issues": ["Auditor mismatch: PDF=KPMG, Record=Unknown"], "confidence_score": 75, "kam_sections_found": 3}, {"record_id": 2, "company": "Deutsche Börse AG", "year": 2017, "file_name": "Pages from DBG-annual-report-2017.pdf", "issues": ["KAM title not found in PDF", "Auditor mismatch: PDF=KPMG, Record=Unknown"], "confidence_score": 45, "kam_sections_found": 3}, {"record_id": 3, "company": "Deutsche Börse AG", "year": 2017, "file_name": "Pages from DBG-annual-report-2017.pdf", "issues": ["KAM title not found in PDF", "Auditor mismatch: PDF=KPMG, Record=Unknown"], "confidence_score": 45, "kam_sections_found": 3}, {"record_id": 4, "company": "Deutsche Börse AG", "year": 2017, "file_name": "Pages from DBG-annual-report-2017.pdf", "issues": ["KAM title not found in PDF", "Auditor mismatch: PDF=KPMG, Record=Unknown"], "confidence_score": 45, "kam_sections_found": 3}, {"record_id": 5, "company": "Deutsche Börse AG", "year": 2017, "file_name": "Pages from DBG-annual-report-2017.pdf", "issues": [], "confidence_score": 100, "kam_sections_found": 3}, {"record_id": 6, "company": "Deutsche Börse AG", "year": 2018, "file_name": "Pages from DBG-annual-report-2018.pdf", "issues": ["KAM title not found in PDF"], "confidence_score": 70, "kam_sections_found": 1}, {"record_id": 7, "company": "Deutsche Börse AG", "year": 2018, "file_name": "Pages from DBG-annual-report-2018.pdf", "issues": ["KAM title not found in PDF"], "confidence_score": 70, "kam_sections_found": 1}, {"record_id": 8, "company": "Deutsche Börse AG", "year": 2018, "file_name": "Pages from DBG-annual-report-2018.pdf", "issues": ["KAM title not found in PDF"], "confidence_score": 70, "kam_sections_found": 1}, {"record_id": 9, "company": "Deutsche Börse AG", "year": 2018, "file_name": "Pages from DBG-annual-report-2018.pdf", "issues": ["KAM title not found in PDF", "Auditor mismatch: PDF=KPMG, Record=Unknown"], "confidence_score": 45, "kam_sections_found": 1}, {"record_id": 10, "company": "Deutsche Börse AG", "year": 2018, "file_name": "Pages from DBG-annual-report-2018.pdf", "issues": ["Auditor mismatch: PDF=KPMG, Record=Unknown"], "confidence_score": 75, "kam_sections_found": 1}, {"record_id": 11, "company": "Deutsche Börse AG", "year": 2018, "file_name": "Pages from DBG-annual-report-2018.pdf", "issues": ["Auditor mismatch: PDF=KPMG, Record=Unknown"], "confidence_score": 75, "kam_sections_found": 1}, {"record_id": 12, "company": "Deutsche Börse AG", "year": 2019, "file_name": "Pages from DBG-annual-report-2019.pdf", "issues": ["KAM title not exact match, similar: ['impairment', 'impairment', 'impairment']", "Auditor mismatch: PDF=KPMG, Record=Unknown"], "confidence_score": 60, "kam_sections_found": 1}, {"record_id": 13, "company": "Deutsche Börse AG", "year": 2019, "file_name": "Pages from DBG-annual-report-2019.pdf", "issues": ["KAM title not found in PDF", "Auditor mismatch: PDF=KPMG, Record=Unknown"], "confidence_score": 45, "kam_sections_found": 1}, {"record_id": 14, "company": "Deutsche Börse AG", "year": 2019, "file_name": "Pages from DBG-annual-report-2019.pdf", "issues": ["KAM title not found in PDF", "Auditor mismatch: PDF=KPMG, Record=Unknown"], "confidence_score": 45, "kam_sections_found": 1}, {"record_id": 15, "company": "Deutsche Börse AG", "year": 2019, "file_name": "Pages from DBG-annual-report-2019.pdf", "issues": [], "confidence_score": 100, "kam_sections_found": 1}, {"record_id": 16, "company": "Deutsche Börse AG", "year": 2020, "file_name": "Pages from DBG-annual-report-2020.pdf", "issues": ["KAM title not found in PDF"], "confidence_score": 70, "kam_sections_found": 1}, {"record_id": 17, "company": "Deutsche Börse AG", "year": 2020, "file_name": "Pages from DBG-annual-report-2020.pdf", "issues": ["KAM title not found in PDF"], "confidence_score": 70, "kam_sections_found": 1}, {"record_id": 18, "company": "Deutsche Börse AG", "year": 2020, "file_name": "Pages from DBG-annual-report-2020.pdf", "issues": ["KAM title not found in PDF"], "confidence_score": 70, "kam_sections_found": 1}, {"record_id": 19, "company": "Deutsche Börse AG", "year": 2020, "file_name": "Pages from DBG-annual-report-2020.pdf", "issues": ["KAM title not found in PDF", "Auditor mismatch: PDF=KPMG, Record=Independent Auditors"], "confidence_score": 45, "kam_sections_found": 1}, {"record_id": 20, "company": "Deutsche Börse AG", "year": 2020, "file_name": "Pages from DBG-annual-report-2020.pdf", "issues": ["KAM title not found in PDF", "Auditor mismatch: PDF=KPMG, Record=Unknown"], "confidence_score": 45, "kam_sections_found": 1}, {"record_id": 21, "company": "Deutsche Börse AG", "year": 2020, "file_name": "Pages from DBG-annual-report-2020.pdf", "issues": ["Auditor mismatch: PDF=KPMG, Record=Unknown"], "confidence_score": 75, "kam_sections_found": 1}, {"record_id": 22, "company": "Deutsche Börse AG", "year": 2021, "file_name": "Pages from DBG-annual-report-2021.pdf", "issues": ["Auditor mismatch: PDF=PwC, Record=Unknown"], "confidence_score": 75, "kam_sections_found": 1}, {"record_id": 23, "company": "Deutsche Börse AG", "year": 2021, "file_name": "Pages from DBG-annual-report-2021.pdf", "issues": ["Auditor mismatch: PDF=PwC, Record=Unknown"], "confidence_score": 75, "kam_sections_found": 1}, {"record_id": 24, "company": "Deutsche Börse AG", "year": 2021, "file_name": "Pages from DBG-annual-report-2021.pdf", "issues": ["KAM title not found in PDF"], "confidence_score": 70, "kam_sections_found": 1}, {"record_id": 25, "company": "Deutsche Börse AG", "year": 2021, "file_name": "Pages from DBG-annual-report-2021.pdf", "issues": ["Auditor mismatch: PDF=PwC, Record=Unknown"], "confidence_score": 75, "kam_sections_found": 1}, {"record_id": 26, "company": "Deutsche Börse AG", "year": 2022, "file_name": "Pages from DBG-annual-report-2022.pdf", "issues": ["Auditor mismatch: PDF=PwC, Record=Unknown"], "confidence_score": 20, "kam_sections_found": 1}, {"record_id": 27, "company": "Deutsche Börse AG", "year": 2022, "file_name": "Pages from DBG-annual-report-2022.pdf", "issues": ["Auditor mismatch: PDF=PwC, Record=Unknown"], "confidence_score": 75, "kam_sections_found": 1}, {"record_id": 28, "company": "Deutsche Börse AG", "year": 2022, "file_name": "Pages from DBG-annual-report-2022.pdf", "issues": ["Auditor mismatch: PDF=PwC, Record=Unknown"], "confidence_score": 75, "kam_sections_found": 1}, {"record_id": 29, "company": "Deutsche Börse AG", "year": 2022, "file_name": "Pages from DBG-annual-report-2024.pdf", "issues": ["Auditor mismatch: PDF=PwC, Record=Independent Auditor"], "confidence_score": 75, "kam_sections_found": 1}, {"record_id": 30, "company": "Deutsche Börse AG", "year": 2022, "file_name": "Pages from DBG-annual-report-2022.pdf", "issues": ["Auditor mismatch: PDF=PwC, Record=Unknown"], "confidence_score": 75, "kam_sections_found": 1}, {"record_id": 31, "company": "Deutsche Börse AG", "year": 2023, "file_name": "Pages from DBG-annual-report-2023.pdf", "issues": ["Auditor mismatch: PDF=PwC, Record=Unknown"], "confidence_score": 75, "kam_sections_found": 1}, {"record_id": 32, "company": "Deutsche Börse AG", "year": 2023, "file_name": "Pages from DBG-annual-report-2023.pdf", "issues": ["Auditor mismatch: PDF=PwC, Record=Unknown"], "confidence_score": 75, "kam_sections_found": 1}, {"record_id": 33, "company": "Deutsche Börse AG", "year": 2023, "file_name": "Pages from DBG-annual-report-2023.pdf", "issues": ["Auditor mismatch: PDF=PwC, Record=Unknown"], "confidence_score": 75, "kam_sections_found": 1}, {"record_id": 34, "company": "Deutsche Börse AG", "year": 2023, "file_name": "Pages from DBG-annual-report-2023.pdf", "issues": ["KAM title not found in PDF", "Auditor mismatch: PDF=PwC, Record=Unknown"], "confidence_score": 45, "kam_sections_found": 1}, {"record_id": 35, "company": "Deutsche Börse AG", "year": 2023, "file_name": "Pages from DBG-annual-report-2023.pdf", "issues": ["Auditor mismatch: PDF=PwC, Record=Unknown"], "confidence_score": 75, "kam_sections_found": 1}, {"record_id": 36, "company": "Deutsche Börse AG", "year": 2023, "file_name": "Pages from DBG-annual-report-2023.pdf", "issues": ["Auditor mismatch: PDF=PwC, Record=Unknown"], "confidence_score": 75, "kam_sections_found": 1}, {"record_id": 37, "company": "Deutsche Börse AG", "year": 2023, "file_name": "Pages from DBG-annual-report-2023.pdf", "issues": ["KAM title not exact match, similar: ['misstatements', 'misstatements', 'misstatement']", "Auditor mismatch: PDF=PwC, Record=Unknown"], "confidence_score": 60, "kam_sections_found": 1}, {"record_id": 38, "company": "Deutsche Börse AG", "year": 2023, "file_name": "Pages from DBG-annual-report-2023.pdf", "issues": ["Auditor mismatch: PDF=PwC, Record=Unknown"], "confidence_score": 75, "kam_sections_found": 1}, {"record_id": 39, "company": "Deutsche Börse AG", "year": 2023, "file_name": "Pages from DBG-annual-report-2023.pdf", "issues": ["Auditor mismatch: PDF=PwC, Record=Unknown"], "confidence_score": 75, "kam_sections_found": 1}, {"record_id": 40, "company": "Deutsche Börse AG", "year": 2023, "file_name": "Pages from DBG-annual-report-2023.pdf", "issues": ["Auditor mismatch: PDF=PwC, Record=Unknown"], "confidence_score": 75, "kam_sections_found": 1}, {"record_id": 41, "company": "Deutsche Börse AG", "year": 2024, "file_name": "Pages from DBG-annual-report-2024.pdf", "issues": ["KAM title not exact match, similar: ['assessments', 'assessments']"], "confidence_score": 85, "kam_sections_found": 1}, {"record_id": 42, "company": "Deutsche Börse AG", "year": 2024, "file_name": "Pages from DBG-annual-report-2024.pdf", "issues": ["Auditor mismatch: PDF=PwC, Record=Independent German public auditor"], "confidence_score": 75, "kam_sections_found": 1}, {"record_id": 43, "company": "Deutsche Börse AG", "year": 2024, "file_name": "Pages from DBG-annual-report-2024.pdf", "issues": ["KAM title not found in PDF"], "confidence_score": 70, "kam_sections_found": 1}, {"record_id": 44, "company": "Deutsche Börse AG", "year": 2024, "file_name": "Pages from DBG-annual-report-2024.pdf", "issues": ["Auditor mismatch: PDF=PwC, Record=Independent German public auditor"], "confidence_score": 75, "kam_sections_found": 1}, {"record_id": 45, "company": "Euronext N.V.", "year": 2014, "file_name": "Pages from EURONEXT_Annual_2015.pdf", "issues": [], "confidence_score": 45, "kam_sections_found": 1}, {"record_id": 46, "company": "Euronext N.V.", "year": 2015, "file_name": "Pages from EURONEXT_Annual_2015.pdf", "issues": [], "confidence_score": 45, "kam_sections_found": 1}, {"record_id": 47, "company": "Euronext N.V.", "year": 2015, "file_name": "Pages from EURONEXT_Annual_2015.pdf", "issues": ["KAM title not found in PDF"], "confidence_score": 70, "kam_sections_found": 1}, {"record_id": 48, "company": "Euronext N.V.", "year": 2015, "file_name": "Pages from EURONEXT_Annual_2015.pdf", "issues": ["KAM title not found in PDF"], "confidence_score": 70, "kam_sections_found": 1}, {"record_id": 49, "company": "Euronext N.V.", "year": 2015, "file_name": "Pages from EURONEXT_Annual_2015.pdf", "issues": ["Auditor mismatch: PDF=PwC, Record=Unknown"], "confidence_score": 75, "kam_sections_found": 1}, {"record_id": 50, "company": "Euronext N.V.", "year": 2015, "file_name": "Pages from EURONEXT_Annual_2015.pdf", "issues": ["KAM title not found in PDF"], "confidence_score": 70, "kam_sections_found": 1}, {"record_id": 51, "company": "Euronext N.V.", "year": 2015, "file_name": "Pages from EURONEXT_Annual_2015.pdf", "issues": ["Auditor mismatch: PDF=PwC, Record=Unknown"], "confidence_score": 75, "kam_sections_found": 1}, {"record_id": 52, "company": "Euronext N.V.", "year": 2015, "file_name": "Pages from EURONEXT_Annual_2015.pdf", "issues": [], "confidence_score": 100, "kam_sections_found": 1}, {"record_id": 53, "company": "Euronext N.V.", "year": 2015, "file_name": "Pages from EURONEXT_Annual_2015.pdf", "issues": ["KAM title not found in PDF", "Auditor mismatch: PDF=PwC, Record=Unknown"], "confidence_score": 45, "kam_sections_found": 1}, {"record_id": 54, "company": "Euronext N.V.", "year": 2015, "file_name": "Pages from EURONEXT_Annual_2015.pdf", "issues": ["KAM title not found in PDF"], "confidence_score": 70, "kam_sections_found": 1}, {"record_id": 55, "company": "Euronext N.V.", "year": 2015, "file_name": "Pages from EURONEXT_Annual_2015.pdf", "issues": ["Auditor mismatch: PDF=PwC, Record=Unknown"], "confidence_score": 75, "kam_sections_found": 1}, {"record_id": 56, "company": "Euronext N.V.", "year": 2015, "file_name": "Pages from EURONEXT_Annual_2015.pdf", "issues": [], "confidence_score": 100, "kam_sections_found": 1}, {"record_id": 57, "company": "Euronext N.V.", "year": 2016, "file_name": "Pages from EURONEXT_Annual_2016.pdf", "issues": ["Auditor mismatch: PDF=PwC, Record=Unknown"], "confidence_score": 20, "kam_sections_found": 1}, {"record_id": 58, "company": "Euronext N.V.", "year": 2016, "file_name": "Pages from EURONEXT_Annual_2016.pdf", "issues": ["KAM title not exact match, similar: ['going-concern']"], "confidence_score": 85, "kam_sections_found": 1}, {"record_id": 59, "company": "Euronext N.V.", "year": 2016, "file_name": "Pages from EURONEXT_Annual_2016.pdf", "issues": ["Auditor mismatch: PDF=PwC, Record=Unknown"], "confidence_score": 75, "kam_sections_found": 1}, {"record_id": 60, "company": "Euronext N.V.", "year": 2016, "file_name": "Pages from EURONEXT_Annual_2016.pdf", "issues": ["KAM title not found in PDF", "Auditor mismatch: PDF=PwC, Record=Unknown"], "confidence_score": 45, "kam_sections_found": 1}, {"record_id": 61, "company": "Euronext N.V.", "year": 2016, "file_name": "Pages from EURONEXT_Annual_2016.pdf", "issues": ["Auditor mismatch: PDF=PwC, Record=Unknown"], "confidence_score": 75, "kam_sections_found": 1}, {"record_id": 62, "company": "Euronext N.V.", "year": 2016, "file_name": "Pages from EURONEXT_Annual_2016.pdf", "issues": ["KAM title not found in PDF"], "confidence_score": 70, "kam_sections_found": 1}, {"record_id": 63, "company": "Euronext N.V.", "year": 2016, "file_name": "Pages from EURONEXT_Annual_2016.pdf", "issues": ["KAM title not exact match, similar: ['determination', 'materiality', 'materiality']", "Auditor mismatch: PDF=PwC, Record=Unknown"], "confidence_score": 60, "kam_sections_found": 1}, {"record_id": 64, "company": "Euronext N.V.", "year": 2016, "file_name": "Pages from EURONEXT_Annual_2016.pdf", "issues": [], "confidence_score": 100, "kam_sections_found": 1}, {"record_id": 65, "company": "Euronext N.V.", "year": 2016, "file_name": "Pages from EURONEXT_Annual_2016.pdf", "issues": ["KAM title not found in PDF", "Auditor mismatch: PDF=PwC, Record=Unknown"], "confidence_score": 45, "kam_sections_found": 1}, {"record_id": 66, "company": "Euronext N.V.", "year": 2016, "file_name": "Pages from EURONEXT_Annual_2016.pdf", "issues": ["KAM title not found in PDF", "Auditor mismatch: PDF=PwC, Record=Unknown"], "confidence_score": 45, "kam_sections_found": 1}, {"record_id": 67, "company": "Euronext N.V.", "year": 2017, "file_name": "Pages from EURONEXT_Annual_2017.pdf", "issues": ["Auditor mismatch: PDF=Ernst & Young, Record=Unknown"], "confidence_score": 75, "kam_sections_found": 1}, {"record_id": 68, "company": "Euronext N.V.", "year": 2017, "file_name": "Pages from EURONEXT_Annual_2017.pdf", "issues": [], "confidence_score": 100, "kam_sections_found": 1}, {"record_id": 69, "company": "Euronext N.V.", "year": 2017, "file_name": "Pages from EURONEXT_Annual_2017.pdf", "issues": ["KAM title not found in PDF"], "confidence_score": 70, "kam_sections_found": 1}, {"record_id": 70, "company": "Euronext N.V.", "year": 2018, "file_name": "Pages from EURONEXT_Annual_2018.pdf", "issues": [], "confidence_score": 100, "kam_sections_found": 1}, {"record_id": 71, "company": "Euronext N.V.", "year": 2018, "file_name": "Pages from EURONEXT_Annual_2018.pdf", "issues": [], "confidence_score": 100, "kam_sections_found": 1}, {"record_id": 72, "company": "Euronext N.V.", "year": 2018, "file_name": "Pages from EURONEXT_Annual_2018.pdf", "issues": [], "confidence_score": 100, "kam_sections_found": 1}, {"record_id": 73, "company": "Euronext N.V.", "year": 2018, "file_name": "Pages from EURONEXT_Annual_2018.pdf", "issues": [], "confidence_score": 100, "kam_sections_found": 1}, {"record_id": 74, "company": "Euronext N.V.", "year": 2019, "file_name": "Pages from EURONEXT_Annual_2019.pdf", "issues": [], "confidence_score": 100, "kam_sections_found": 1}, {"record_id": 75, "company": "Euronext N.V.", "year": 2019, "file_name": "Pages from EURONEXT_Annual_2019.pdf", "issues": [], "confidence_score": 100, "kam_sections_found": 1}, {"record_id": 76, "company": "Euronext N.V.", "year": 2019, "file_name": "Pages from EURONEXT_Annual_2019.pdf", "issues": [], "confidence_score": 100, "kam_sections_found": 1}, {"record_id": 77, "company": "Euronext N.V.", "year": 2020, "file_name": "Pages from EURONEXT_Annual_2020.pdf", "issues": ["Auditor mismatch: PDF=Ernst & Young, Record=Unknown"], "confidence_score": 75, "kam_sections_found": 1}, {"record_id": 78, "company": "Euronext N.V.", "year": 2020, "file_name": "Pages from EURONEXT_Annual_2020.pdf", "issues": ["KAM title not found in PDF", "Auditor mismatch: PDF=Ernst & Young, Record=Unknown"], "confidence_score": 45, "kam_sections_found": 1}, {"record_id": 79, "company": "Euronext N.V.", "year": 2020, "file_name": "Pages from EURONEXT_Annual_2020.pdf", "issues": [], "confidence_score": 100, "kam_sections_found": 1}, {"record_id": 80, "company": "Euronext N.V.", "year": 2020, "file_name": "Pages from EURONEXT_Annual_2020.pdf", "issues": ["KAM title not exact match, similar: ['determination', 'materiality', 'materiality']"], "confidence_score": 85, "kam_sections_found": 1}, {"record_id": 81, "company": "Euronext N.V.", "year": 2020, "file_name": "Pages from EURONEXT_Annual_2020.pdf", "issues": ["Auditor mismatch: PDF=Ernst & Young, Record=Unknown"], "confidence_score": 75, "kam_sections_found": 1}, {"record_id": 82, "company": "Euronext N.V.", "year": 2020, "file_name": "Pages from EURONEXT_Annual_2020.pdf", "issues": ["Auditor mismatch: PDF=Ernst & Young, Record=Unknown"], "confidence_score": 75, "kam_sections_found": 1}, {"record_id": 83, "company": "Euronext N.V.", "year": 2020, "file_name": "Pages from EURONEXT_Annual_2020.pdf", "issues": ["KAM title not found in PDF"], "confidence_score": 70, "kam_sections_found": 1}, {"record_id": 84, "company": "Euronext N.V.", "year": 2020, "file_name": "Pages from EURONEXT_Annual_2020.pdf", "issues": ["Auditor mismatch: PDF=Ernst & Young, Record=Unknown"], "confidence_score": 75, "kam_sections_found": 1}, {"record_id": 85, "company": "Euronext N.V.", "year": 2021, "file_name": "Pages from EURONEXT_Annual_2021.pdf", "issues": [], "confidence_score": 100, "kam_sections_found": 1}, {"record_id": 86, "company": "Euronext N.V.", "year": 2021, "file_name": "Pages from EURONEXT_Annual_2020.pdf", "issues": ["KAM title not found in PDF"], "confidence_score": 70, "kam_sections_found": 1}, {"record_id": 87, "company": "Euronext N.V.", "year": 2021, "file_name": "Pages from EURONEXT_Annual_2021.pdf", "issues": ["KAM title not found in PDF", "Auditor mismatch: PDF=Ernst & Young, Record=Unknown"], "confidence_score": 45, "kam_sections_found": 1}, {"record_id": 88, "company": "Euronext N.V.", "year": 2021, "file_name": "Pages from EURONEXT_Annual_2021.pdf", "issues": ["Auditor mismatch: PDF=Ernst & Young, Record=Unknown"], "confidence_score": 75, "kam_sections_found": 1}, {"record_id": 89, "company": "Euronext N.V.", "year": 2021, "file_name": "Pages from EURONEXT_Annual_2021.pdf", "issues": ["KAM title not found in PDF"], "confidence_score": 70, "kam_sections_found": 1}, {"record_id": 90, "company": "Euronext N.V.", "year": 2021, "file_name": "Pages from EURONEXT_Annual_2020.pdf", "issues": ["KAM title not found in PDF"], "confidence_score": 70, "kam_sections_found": 1}, {"record_id": 91, "company": "Euronext N.V.", "year": 2021, "file_name": "Pages from EURONEXT_Annual_2021.pdf", "issues": ["KAM title not found in PDF"], "confidence_score": 70, "kam_sections_found": 1}, {"record_id": 92, "company": "Euronext N.V.", "year": 2021, "file_name": "Pages from EURONEXT_Annual_2020.pdf", "issues": ["KAM title not found in PDF"], "confidence_score": 70, "kam_sections_found": 1}, {"record_id": 93, "company": "Euronext N.V.", "year": 2021, "file_name": "Pages from EURONEXT_Annual_2021.pdf", "issues": [], "confidence_score": 100, "kam_sections_found": 1}, {"record_id": 94, "company": "Euronext N.V.", "year": 2021, "file_name": "Pages from EURONEXT_Annual_2021.pdf", "issues": [], "confidence_score": 100, "kam_sections_found": 1}, {"record_id": 95, "company": "Euronext N.V.", "year": 2021, "file_name": "Pages from EURONEXT_Annual_2021.pdf", "issues": ["KAM title not found in PDF"], "confidence_score": 70, "kam_sections_found": 1}, {"record_id": 96, "company": "Euronext N.V.", "year": 2021, "file_name": "Pages from EURONEXT_Annual_2021.pdf", "issues": [], "confidence_score": 100, "kam_sections_found": 1}, {"record_id": 97, "company": "Euronext N.V.", "year": 2022, "file_name": "Pages from EURONEXT_Annual_2022.pdf", "issues": [], "confidence_score": 100, "kam_sections_found": 1}, {"record_id": 98, "company": "Euronext N.V.", "year": 2022, "file_name": "Pages from EURONEXT_Annual_2022.pdf", "issues": [], "confidence_score": 100, "kam_sections_found": 1}, {"record_id": 99, "company": "Euronext N.V.", "year": 2022, "file_name": "Pages from EURONEXT_Annual_2022.pdf", "issues": [], "confidence_score": 100, "kam_sections_found": 1}, {"record_id": 100, "company": "Euronext N.V.", "year": 2022, "file_name": "Pages from EURONEXT_Annual_2022.pdf", "issues": [], "confidence_score": 100, "kam_sections_found": 1}, {"record_id": 101, "company": "Euronext N.V.", "year": 2023, "file_name": "Pages from EURONEXT_Annual_2023.pdf", "issues": ["KAM title not found in PDF", "Low description word overlap with PDF", "Could not identify auditor from PDF", "No KAM sections identified in PDF"], "confidence_score": 0}, {"record_id": 102, "company": "Euronext N.V.", "year": 2023, "file_name": "Pages from EURONEXT_Annual_2023.pdf", "issues": ["KAM title not found in PDF", "Low description word overlap with PDF", "Could not identify auditor from PDF", "No KAM sections identified in PDF"], "confidence_score": 0}, {"record_id": 103, "company": "Euronext N.V.", "year": 2023, "file_name": "Pages from EURONEXT_Annual_2023.pdf", "issues": ["KAM title not found in PDF", "Low description word overlap with PDF", "Could not identify auditor from PDF", "No KAM sections identified in PDF"], "confidence_score": 0}, {"record_id": 104, "company": "Euronext N.V.", "year": 2023, "file_name": "Pages from EURONEXT_Annual_2023.pdf", "issues": ["KAM title not found in PDF", "Low description word overlap with PDF", "Could not identify auditor from PDF", "No KAM sections identified in PDF"], "confidence_score": 0}, {"record_id": 105, "company": "Euronext N.V.", "year": 2023, "file_name": "Pages from EURONEXT_Annual_2023.pdf", "issues": ["KAM title not found in PDF", "Low description word overlap with PDF", "Could not identify auditor from PDF", "No KAM sections identified in PDF"], "confidence_score": 0}, {"record_id": 106, "company": "Euronext N.V.", "year": 2023, "file_name": "Pages from EURONEXT_Annual_2023.pdf", "issues": ["KAM title not found in PDF", "Low description word overlap with PDF", "Could not identify auditor from PDF", "No KAM sections identified in PDF"], "confidence_score": 0}, {"record_id": 107, "company": "Euronext N.V.", "year": 2023, "file_name": "Pages from EURONEXT_Annual_2023.pdf", "issues": ["KAM title not found in PDF", "Low description word overlap with PDF", "Could not identify auditor from PDF", "No KAM sections identified in PDF"], "confidence_score": 0}, {"record_id": 108, "company": "Euronext N.V.", "year": 2023, "file_name": "Pages from EURONEXT_Annual_2023.pdf", "issues": ["KAM title not found in PDF", "Low description word overlap with PDF", "Could not identify auditor from PDF", "No KAM sections identified in PDF"], "confidence_score": 0}, {"record_id": 109, "company": "Euronext N.V.", "year": 2024, "file_name": "Pages from EURONEXT_Annual_2024.pdf", "issues": ["KAM title not found in PDF", "Low description word overlap with PDF", "Could not identify auditor from PDF", "No KAM sections identified in PDF"], "confidence_score": 0}, {"record_id": 110, "company": "Euronext N.V.", "year": 2024, "file_name": "Pages from EURONEXT_Annual_2024.pdf", "issues": ["KAM title not found in PDF", "Low description word overlap with PDF", "Could not identify auditor from PDF", "No KAM sections identified in PDF"], "confidence_score": 0}, {"record_id": 111, "company": "HKEX", "year": 2015, "file_name": "Pages from HKEX_Annual_2015.pdf", "issues": [], "confidence_score": 45, "kam_sections_found": 1}, {"record_id": 112, "company": "HKEX", "year": 2016, "file_name": "Pages from HKEX_Annual_2016.pdf", "issues": ["Auditor mismatch: PDF=PwC, Record=Unknown"], "confidence_score": 75, "kam_sections_found": 1}, {"record_id": 113, "company": "HKEX", "year": 2016, "file_name": "Pages from HKEX_Annual_2016.pdf", "issues": ["KAM title not found in PDF", "Auditor mismatch: PDF=PwC, Record=Unknown"], "confidence_score": 45, "kam_sections_found": 1}, {"record_id": 114, "company": "HKEX", "year": 2016, "file_name": "Pages from HKEX_Annual_2016.pdf", "issues": ["Auditor mismatch: PDF=PwC, Record=Unknown"], "confidence_score": 75, "kam_sections_found": 1}, {"record_id": 115, "company": "HKEX", "year": 2016, "file_name": "Pages from HKEX_Annual_2016.pdf", "issues": [], "confidence_score": 100, "kam_sections_found": 1}, {"record_id": 116, "company": "HKEX", "year": 2017, "file_name": "Pages from HKEX_Annual_2017.pdf", "issues": ["Auditor mismatch: PDF=PwC, Record=Unknown"], "confidence_score": 75, "kam_sections_found": 1}, {"record_id": 117, "company": "HKEX", "year": 2017, "file_name": "Pages from HKEX_Annual_2017.pdf", "issues": ["KAM title not found in PDF", "Auditor mismatch: PDF=PwC, Record=Unknown"], "confidence_score": 45, "kam_sections_found": 1}, {"record_id": 118, "company": "HKEX", "year": 2017, "file_name": "Pages from HKEX_Annual_2017.pdf", "issues": ["KAM title not found in PDF", "Auditor mismatch: PDF=PwC, Record=Unknown"], "confidence_score": 45, "kam_sections_found": 1}, {"record_id": 119, "company": "HKEX", "year": 2018, "file_name": "Pages from HKEX_Annual_2018.pdf", "issues": ["Auditor mismatch: PDF=PwC, Record=Unknown"], "confidence_score": 75, "kam_sections_found": 1}, {"record_id": 120, "company": "HKEX", "year": 2018, "file_name": "Pages from HKEX_Annual_2018.pdf", "issues": ["Auditor mismatch: PDF=PwC, Record=Unknown"], "confidence_score": 75, "kam_sections_found": 1}, {"record_id": 121, "company": "HKEX", "year": 2018, "file_name": "Pages from HKEX_Annual_2018.pdf", "issues": ["Auditor mismatch: PDF=PwC, Record=Unknown"], "confidence_score": 75, "kam_sections_found": 1}, {"record_id": 122, "company": "HKEX", "year": 2018, "file_name": "Pages from HKEX_Annual_2018.pdf", "issues": ["Auditor mismatch: PDF=PwC, Record=Unknown"], "confidence_score": 75, "kam_sections_found": 1}, {"record_id": 123, "company": "HKEX", "year": 2019, "file_name": "Pages from HKEX_Annual_2019.pdf", "issues": ["Auditor mismatch: PDF=PwC, Record=Unknown"], "confidence_score": 20, "kam_sections_found": 1}, {"record_id": 124, "company": "HKEX", "year": 2019, "file_name": "Pages from HKEX_Annual_2019.pdf", "issues": [], "confidence_score": 100, "kam_sections_found": 1}, {"record_id": 125, "company": "HKEX", "year": 2019, "file_name": "Pages from HKEX_Annual_2019.pdf", "issues": [], "confidence_score": 100, "kam_sections_found": 1}, {"record_id": 126, "company": "HKEX", "year": 2020, "file_name": "Pages from HKEX_Annual_2020.pdf", "issues": [], "confidence_score": 100, "kam_sections_found": 1}, {"record_id": 127, "company": "HKEX", "year": 2020, "file_name": "Pages from HKEX_Annual_2020.pdf", "issues": ["Auditor mismatch: PDF=PwC, Record=Unknown"], "confidence_score": 75, "kam_sections_found": 1}, {"record_id": 128, "company": "HKEX", "year": 2020, "file_name": "Pages from HKEX_Annual_2020.pdf", "issues": [], "confidence_score": 100, "kam_sections_found": 1}, {"record_id": 129, "company": "HKEX", "year": 2020, "file_name": "Pages from HKEX_Annual_2020.pdf", "issues": ["Auditor mismatch: PDF=PwC, Record=Unknown"], "confidence_score": 75, "kam_sections_found": 1}, {"record_id": 130, "company": "HKEX", "year": 2021, "file_name": "Pages from HKEX_Annual_2021.pdf", "issues": ["Auditor mismatch: PDF=PwC, Record=Unknown"], "confidence_score": 75, "kam_sections_found": 1}, {"record_id": 131, "company": "HKEX", "year": 2021, "file_name": "Pages from HKEX_Annual_2021.pdf", "issues": ["Auditor mismatch: PDF=PwC, Record=Unknown"], "confidence_score": 75, "kam_sections_found": 1}, {"record_id": 132, "company": "HKEX", "year": 2022, "file_name": "Pages from HKEX_Annual_2021.pdf", "issues": [], "confidence_score": 100, "kam_sections_found": 1}, {"record_id": 133, "company": "HKEX", "year": 2022, "file_name": "Pages from HKEX_Annual_2022.pdf", "issues": ["Auditor mismatch: PDF=PwC, Record=Unknown"], "confidence_score": 75, "kam_sections_found": 1}, {"record_id": 134, "company": "HKEX", "year": 2022, "file_name": "Pages from HKEX_Annual_2022.pdf", "issues": [], "confidence_score": 100, "kam_sections_found": 1}, {"record_id": 135, "company": "HKEX", "year": 2022, "file_name": "Pages from HKEX_Annual_2021.pdf", "issues": [], "confidence_score": 100, "kam_sections_found": 1}, {"record_id": 136, "company": "HKEX", "year": 2022, "file_name": "Pages from HKEX_Annual_2022.pdf", "issues": ["Auditor mismatch: PDF=PwC, Record=Unknown"], "confidence_score": 75, "kam_sections_found": 1}, {"record_id": 137, "company": "HKEX", "year": 2022, "file_name": "Pages from HKEX_Annual_2022.pdf", "issues": [], "confidence_score": 100, "kam_sections_found": 1}, {"record_id": 138, "company": "HKEX", "year": 2023, "file_name": "Pages from HKEX_Annual_2023.pdf", "issues": ["Auditor mismatch: PDF=PwC, Record=Unknown"], "confidence_score": 75, "kam_sections_found": 1}, {"record_id": 139, "company": "HKEX", "year": 2023, "file_name": "Pages from HKEX_Annual_2023.pdf", "issues": ["Auditor mismatch: PDF=PwC, Record=Unknown"], "confidence_score": 75, "kam_sections_found": 1}, {"record_id": 140, "company": "HKEX", "year": 2023, "file_name": "Pages from HKEX_Annual_2023.pdf", "issues": ["Auditor mismatch: PDF=PwC, Record=Unknown"], "confidence_score": 75, "kam_sections_found": 1}, {"record_id": 141, "company": "HKEX", "year": 2024, "file_name": "Pages from HKEX_Annual_2024.pdf", "issues": ["Auditor mismatch: PDF=PwC, Record=Unknown"], "confidence_score": 75, "kam_sections_found": 1}, {"record_id": 142, "company": "HKEX", "year": 2024, "file_name": "Pages from HKEX_Annual_2024.pdf", "issues": ["Auditor mismatch: PDF=PwC, Record=Unknown"], "confidence_score": 75, "kam_sections_found": 1}, {"record_id": 143, "company": "HKEX", "year": 2024, "file_name": "Pages from HKEX_Annual_2024.pdf", "issues": ["Auditor mismatch: PDF=PwC, Record=Unknown"], "confidence_score": 75, "kam_sections_found": 1}, {"record_id": 144, "company": "HKEX", "year": 2024, "file_name": "Pages from HKEX_Annual_2023.pdf", "issues": ["KAM title not found in PDF"], "confidence_score": 70, "kam_sections_found": 1}, {"record_id": 145, "company": "HKEX", "year": 2025, "file_name": "Pages from HKEX_Annual_2024.pdf", "issues": ["KAM title not found in PDF"], "confidence_score": 70, "kam_sections_found": 1}, {"record_id": 146, "company": "ICE", "year": 2015, "file_name": "Pages from ICE_Annual_2015.pdf", "issues": ["Could not identify auditor from PDF", "No KAM sections identified in PDF"], "confidence_score": 0}, {"record_id": 147, "company": "ICE", "year": 2016, "file_name": "Pages from ICE_Annual_2016.pdf", "issues": [], "confidence_score": 45, "kam_sections_found": 1}, {"record_id": 148, "company": "ICE", "year": 2017, "file_name": "Pages from ICE_Annual_2017.pdf", "issues": [], "confidence_score": 45, "kam_sections_found": 1}, {"record_id": 149, "company": "ICE", "year": 2018, "file_name": "Pages from ICE_Annual_2018.pdf", "issues": ["Could not identify auditor from PDF"], "confidence_score": 20, "kam_sections_found": 1}, {"record_id": 150, "company": "ICE", "year": 2019, "file_name": "Pages from ICE_Annual_2019.pdf", "issues": ["KAM title not exact match, similar: ['itsacquisition']"], "confidence_score": 85, "kam_sections_found": 1}, {"record_id": 151, "company": "ICE", "year": 2019, "file_name": "Pages from ICE_Annual_2019.pdf", "issues": ["KAM title not found in PDF"], "confidence_score": 70, "kam_sections_found": 1}, {"record_id": 152, "company": "ICE", "year": 2019, "file_name": "Pages from ICE_Annual_2019.pdf", "issues": ["KAM title not found in PDF", "Auditor mismatch: PDF=Ernst & Young, Record=Unknown"], "confidence_score": 45, "kam_sections_found": 1}, {"record_id": 153, "company": "ICE", "year": 2020, "file_name": "Pages from ICE_Annual_2020.pdf", "issues": ["Auditor mismatch: PDF=Ernst & Young, Record=Independent Registered Public Accounting Firm"], "confidence_score": 20, "kam_sections_found": 1}, {"record_id": 154, "company": "ICE", "year": 2020, "file_name": "Pages from ICE_Annual_2020.pdf", "issues": ["KAM title not exact match, similar: ['untingforacquisitionofelliemae']"], "confidence_score": 85, "kam_sections_found": 1}, {"record_id": 155, "company": "ICE", "year": 2020, "file_name": "Pages from ICE_Annual_2020.pdf", "issues": ["KAM title not exact match, similar: ['untingforincometaxes']"], "confidence_score": 85, "kam_sections_found": 1}, {"record_id": 156, "company": "ICE", "year": 2021, "file_name": "Pages from ICE_Annual_2021.pdf", "issues": ["Auditor mismatch: PDF=Ernst & Young, Record=Independent Registered Public Accounting Firm", "No KAM sections identified in PDF"], "confidence_score": 0}, {"record_id": 157, "company": "ICE", "year": 2021, "file_name": "Pages from ICE_Annual_2021.pdf", "issues": ["KAM title not exact match, similar: ['untingforincometaxes', \"'saccountingforconsoli\"]", "Low description word overlap with PDF", "No KAM sections identified in PDF"], "confidence_score": 40}, {"record_id": 158, "company": "ICE", "year": 2022, "file_name": "Pages from ICE_Annual_2022.pdf", "issues": ["Could not identify auditor from PDF", "No KAM sections identified in PDF"], "confidence_score": 0}, {"record_id": 159, "company": "ICE", "year": 2022, "file_name": "Pages from ICE_Annual_2022.pdf", "issues": ["KAM title not exact match, similar: ['accountittniigfoffriniicometattxes']", "Low description word overlap with PDF", "Could not identify auditor from PDF", "No KAM sections identified in PDF"], "confidence_score": 15}, {"record_id": 160, "company": "ICE", "year": 2023, "file_name": "Pages from ICE_Annual_2023.pdf", "issues": ["Auditor mismatch: PDF=Ernst & Young, Record=Independent Registered Public Accounting Firm", "No KAM sections identified in PDF"], "confidence_score": 0}, {"record_id": 161, "company": "ICE", "year": 2023, "file_name": "Pages from ICE_Annual_2023.pdf", "issues": ["KAM title not exact match, similar: ['accountinttgforbusineiisscombinatiottns', 'accountinttgforincometaxes']", "Low description word overlap with PDF", "No KAM sections identified in PDF"], "confidence_score": 40}, {"record_id": 162, "company": "ICE", "year": 2023, "file_name": "Pages from ICE_Annual_2023.pdf", "issues": ["KAM title not exact match, similar: ['accountinttgforincometaxes', 'accountinttgforbusineiisscombinatiottns']", "Low description word overlap with PDF", "No KAM sections identified in PDF"], "confidence_score": 40}, {"record_id": 163, "company": "ICE", "year": 2024, "file_name": "Pages from ICE_Annual_2024.pdf", "issues": ["KAM title not exact match, similar: ['accountingforincometaxes']", "Low description word overlap with PDF", "No KAM sections identified in PDF"], "confidence_score": 40}, {"record_id": 164, "company": "LSEG", "year": 2015, "file_name": "Pages from LSEG_Annual_2015.pdf", "issues": [], "confidence_score": 100, "kam_sections_found": 1}, {"record_id": 165, "company": "LSEG", "year": 2015, "file_name": "Pages from LSEG_Annual_2015.pdf", "issues": ["KAM title not found in PDF"], "confidence_score": 70, "kam_sections_found": 1}, {"record_id": 166, "company": "LSEG", "year": 2015, "file_name": "Pages from LSEG_Annual_2015.pdf", "issues": ["KAM title not found in PDF"], "confidence_score": 70, "kam_sections_found": 1}, {"record_id": 167, "company": "LSEG", "year": 2015, "file_name": "Pages from LSEG_Annual_2015.pdf", "issues": ["Auditor mismatch: PDF=Ernst & Young, Record=Unknown"], "confidence_score": 75, "kam_sections_found": 1}, {"record_id": 168, "company": "LSEG", "year": 2015, "file_name": "Pages from LSEG_Annual_2015.pdf", "issues": ["KAM title not found in PDF"], "confidence_score": 70, "kam_sections_found": 1}, {"record_id": 169, "company": "LSEG", "year": 2015, "file_name": "Pages from LSEG_Annual_2015.pdf", "issues": ["KAM title not found in PDF", "Auditor mismatch: PDF=Ernst & Young, Record=Unknown"], "confidence_score": 45, "kam_sections_found": 1}, {"record_id": 170, "company": "LSEG", "year": 2015, "file_name": "Pages from LSEG_Annual_2015.pdf", "issues": ["KAM title not found in PDF", "Auditor mismatch: PDF=Ernst & Young, Record=Unknown"], "confidence_score": 45, "kam_sections_found": 1}, {"record_id": 171, "company": "LSEG", "year": 2016, "file_name": "Pages from LSEG_Annual_2016.pdf", "issues": ["KAM title not found in PDF"], "confidence_score": 70, "kam_sections_found": 1}, {"record_id": 172, "company": "LSEG", "year": 2016, "file_name": "Pages from LSEG_Annual_2016.pdf", "issues": ["KAM title not found in PDF"], "confidence_score": 70, "kam_sections_found": 1}, {"record_id": 173, "company": "LSEG", "year": 2016, "file_name": "Pages from LSEG_Annual_2016.pdf", "issues": ["Auditor mismatch: PDF=Ernst & Young, Record=Unknown"], "confidence_score": 75, "kam_sections_found": 1}, {"record_id": 174, "company": "LSEG", "year": 2016, "file_name": "Pages from LSEG_Annual_2016.pdf", "issues": [], "confidence_score": 100, "kam_sections_found": 1}, {"record_id": 175, "company": "LSEG", "year": 2016, "file_name": "Pages from LSEG_Annual_2016.pdf", "issues": ["Auditor mismatch: PDF=Ernst & Young, Record=Unknown"], "confidence_score": 75, "kam_sections_found": 1}, {"record_id": 176, "company": "LSEG", "year": 2016, "file_name": "Pages from LSEG_Annual_2016.pdf", "issues": [], "confidence_score": 100, "kam_sections_found": 1}, {"record_id": 177, "company": "LSEG", "year": 2016, "file_name": "Pages from LSEG_Annual_2016.pdf", "issues": ["Auditor mismatch: PDF=Ernst & Young, Record=Unknown"], "confidence_score": 75, "kam_sections_found": 1}, {"record_id": 178, "company": "LSEG", "year": 2016, "file_name": "Pages from LSEG_Annual_2016.pdf", "issues": ["KAM title not found in PDF", "Auditor mismatch: PDF=Ernst & Young, Record=Unknown"], "confidence_score": 45, "kam_sections_found": 1}, {"record_id": 179, "company": "LSEG", "year": 2017, "file_name": "Pages from LSEG_Annual_2016.pdf", "issues": ["KAM title not found in PDF"], "confidence_score": 70, "kam_sections_found": 1}, {"record_id": 180, "company": "LSEG", "year": 2017, "file_name": "Pages from LSEG_Annual_2016.pdf", "issues": ["KAM title not found in PDF"], "confidence_score": 70, "kam_sections_found": 1}, {"record_id": 181, "company": "LSEG", "year": 2017, "file_name": "Pages from LSEG_Annual_2016.pdf", "issues": ["KAM title not found in PDF"], "confidence_score": 70, "kam_sections_found": 1}, {"record_id": 182, "company": "LSEG", "year": 2017, "file_name": "Pages from LSEG_Annual_2016.pdf", "issues": ["KAM title not found in PDF"], "confidence_score": 70, "kam_sections_found": 1}, {"record_id": 183, "company": "LSEG", "year": 2017, "file_name": "Pages from LSEG_Annual_2017.pdf", "issues": ["Auditor mismatch: PDF=Ernst & Young, Record=Unknown"], "confidence_score": 75, "kam_sections_found": 1}, {"record_id": 184, "company": "LSEG", "year": 2017, "file_name": "Pages from LSEG_Annual_2017.pdf", "issues": [], "confidence_score": 100, "kam_sections_found": 1}, {"record_id": 185, "company": "LSEG", "year": 2017, "file_name": "Pages from LSEG_Annual_2017.pdf", "issues": ["KAM title not found in PDF", "Auditor mismatch: PDF=Ernst & Young, Record=Unknown"], "confidence_score": 45, "kam_sections_found": 1}, {"record_id": 186, "company": "LSEG", "year": 2017, "file_name": "Pages from LSEG_Annual_2017.pdf", "issues": ["KAM title not found in PDF"], "confidence_score": 70, "kam_sections_found": 1}, {"record_id": 187, "company": "LSEG", "year": 2017, "file_name": "Pages from LSEG_Annual_2017.pdf", "issues": ["KAM title not found in PDF", "Auditor mismatch: PDF=Ernst & Young, Record=Unknown"], "confidence_score": 45, "kam_sections_found": 1}, {"record_id": 188, "company": "LSEG", "year": 2017, "file_name": "Pages from LSEG_Annual_2017.pdf", "issues": ["KAM title not found in PDF", "Auditor mismatch: PDF=Ernst & Young, Record=Unknown"], "confidence_score": 45, "kam_sections_found": 1}, {"record_id": 189, "company": "LSEG", "year": 2018, "file_name": "Pages from LSEG_Annual_2018.pdf", "issues": ["KAM title not found in PDF"], "confidence_score": 70, "kam_sections_found": 1}, {"record_id": 190, "company": "LSEG", "year": 2018, "file_name": "Pages from LSEG_Annual_2018.pdf", "issues": [], "confidence_score": 100, "kam_sections_found": 1}, {"record_id": 191, "company": "LSEG", "year": 2018, "file_name": "Pages from LSEG_Annual_2018.pdf", "issues": [], "confidence_score": 100, "kam_sections_found": 1}, {"record_id": 192, "company": "LSEG", "year": 2018, "file_name": "Pages from LSEG_Annual_2018.pdf", "issues": ["KAM title not found in PDF", "Auditor mismatch: PDF=Ernst & Young, Record=Unknown"], "confidence_score": 45, "kam_sections_found": 1}, {"record_id": 193, "company": "LSEG", "year": 2018, "file_name": "Pages from LSEG_Annual_2018.pdf", "issues": ["KAM title not found in PDF", "Auditor mismatch: PDF=Ernst & Young, Record=Unknown"], "confidence_score": 45, "kam_sections_found": 1}, {"record_id": 194, "company": "LSEG", "year": 2018, "file_name": "Pages from LSEG_Annual_2018.pdf", "issues": ["KAM title not found in PDF", "Auditor mismatch: PDF=Ernst & Young, Record=Unknown"], "confidence_score": 45, "kam_sections_found": 1}, {"record_id": 195, "company": "LSEG", "year": 2018, "file_name": "Pages from LSEG_Annual_2018.pdf", "issues": ["KAM title not found in PDF"], "confidence_score": 70, "kam_sections_found": 1}, {"record_id": 196, "company": "LSEG", "year": 2018, "file_name": "Pages from LSEG_Annual_2018.pdf", "issues": ["KAM title not found in PDF", "Auditor mismatch: PDF=Ernst & Young, Record=Unknown"], "confidence_score": 45, "kam_sections_found": 1}, {"record_id": 197, "company": "LSEG", "year": 2018, "file_name": "Pages from LSEG_Annual_2018.pdf", "issues": [], "confidence_score": 100, "kam_sections_found": 1}, {"record_id": 198, "company": "LSEG", "year": 2018, "file_name": "Pages from LSEG_Annual_2018.pdf", "issues": ["KAM title not found in PDF"], "confidence_score": 70, "kam_sections_found": 1}, {"record_id": 199, "company": "LSEG", "year": 2019, "file_name": "Pages from LSEG_Annual_2019.pdf", "issues": ["KAM title not found in PDF"], "confidence_score": 70, "kam_sections_found": 1}, {"record_id": 200, "company": "LSEG", "year": 2019, "file_name": "Pages from LSEG_Annual_2019.pdf", "issues": ["KAM title not found in PDF"], "confidence_score": 70, "kam_sections_found": 1}, {"record_id": 201, "company": "LSEG", "year": 2019, "file_name": "Pages from LSEG_Annual_2019.pdf", "issues": ["KAM title not found in PDF"], "confidence_score": 70, "kam_sections_found": 1}, {"record_id": 202, "company": "LSEG", "year": 2019, "file_name": "Pages from LSEG_Annual_2019.pdf", "issues": ["KAM title not found in PDF"], "confidence_score": 70, "kam_sections_found": 1}, {"record_id": 203, "company": "LSEG", "year": 2019, "file_name": "Pages from LSEG_Annual_2019.pdf", "issues": [], "confidence_score": 100, "kam_sections_found": 1}, {"record_id": 204, "company": "LSEG", "year": 2019, "file_name": "Pages from LSEG_Annual_2019.pdf", "issues": ["KAM title not found in PDF"], "confidence_score": 70, "kam_sections_found": 1}, {"record_id": 205, "company": "LSEG", "year": 2019, "file_name": "Pages from LSEG_Annual_2019.pdf", "issues": ["KAM title not found in PDF"], "confidence_score": 70, "kam_sections_found": 1}, {"record_id": 206, "company": "LSEG", "year": 2019, "file_name": "Pages from LSEG_Annual_2019.pdf", "issues": ["KAM title not found in PDF"], "confidence_score": 70, "kam_sections_found": 1}, {"record_id": 207, "company": "LSEG", "year": 2019, "file_name": "Pages from LSEG_Annual_2019.pdf", "issues": ["KAM title not found in PDF"], "confidence_score": 70, "kam_sections_found": 1}, {"record_id": 208, "company": "LSEG", "year": 2020, "file_name": "Pages from LSEG_Annual_2020.pdf", "issues": ["KAM title not found in PDF"], "confidence_score": 70, "kam_sections_found": 1}, {"record_id": 209, "company": "LSEG", "year": 2020, "file_name": "Pages from LSEG_Annual_2020.pdf", "issues": ["KAM title not found in PDF"], "confidence_score": 70, "kam_sections_found": 1}, {"record_id": 210, "company": "LSEG", "year": 2020, "file_name": "Pages from LSEG_Annual_2020.pdf", "issues": ["KAM title not found in PDF"], "confidence_score": 70, "kam_sections_found": 1}, {"record_id": 211, "company": "LSEG", "year": 2020, "file_name": "Pages from LSEG_Annual_2020.pdf", "issues": [], "confidence_score": 100, "kam_sections_found": 1}, {"record_id": 212, "company": "LSEG", "year": 2020, "file_name": "Pages from LSEG_Annual_2020.pdf", "issues": [], "confidence_score": 100, "kam_sections_found": 1}, {"record_id": 213, "company": "LSEG", "year": 2020, "file_name": "Pages from LSEG_Annual_2020.pdf", "issues": [], "confidence_score": 100, "kam_sections_found": 1}, {"record_id": 214, "company": "LSEG", "year": 2020, "file_name": "Pages from LSEG_Annual_2020.pdf", "issues": [], "confidence_score": 100, "kam_sections_found": 1}, {"record_id": 215, "company": "LSEG", "year": 2020, "file_name": "Pages from LSEG_Annual_2020.pdf", "issues": [], "confidence_score": 100, "kam_sections_found": 1}, {"record_id": 216, "company": "LSEG", "year": 2021, "file_name": "Pages from LSEG_Annual_2021.pdf", "issues": ["KAM title not found in PDF"], "confidence_score": 70, "kam_sections_found": 1}, {"record_id": 217, "company": "LSEG", "year": 2021, "file_name": "Pages from LSEG_Annual_2023.pdf", "issues": ["KAM title not found in PDF"], "confidence_score": 70, "kam_sections_found": 1}, {"record_id": 218, "company": "LSEG", "year": 2021, "file_name": "Pages from LSEG_Annual_2021.pdf", "issues": ["KAM title not found in PDF"], "confidence_score": 70, "kam_sections_found": 1}, {"record_id": 219, "company": "LSEG", "year": 2021, "file_name": "Pages from LSEG_Annual_2021.pdf", "issues": ["KAM title not found in PDF"], "confidence_score": 70, "kam_sections_found": 1}, {"record_id": 220, "company": "LSEG", "year": 2021, "file_name": "Pages from LSEG_Annual_2021.pdf", "issues": ["KAM title not found in PDF"], "confidence_score": 70, "kam_sections_found": 1}, {"record_id": 221, "company": "LSEG", "year": 2021, "file_name": "Pages from LSEG_Annual_2021.pdf", "issues": ["KAM title not found in PDF"], "confidence_score": 70, "kam_sections_found": 1}, {"record_id": 222, "company": "LSEG", "year": 2021, "file_name": "Pages from LSEG_Annual_2021.pdf", "issues": ["KAM title not found in PDF"], "confidence_score": 70, "kam_sections_found": 1}, {"record_id": 223, "company": "LSEG", "year": 2021, "file_name": "Pages from LSEG_Annual_2021.pdf", "issues": ["KAM title not found in PDF"], "confidence_score": 70, "kam_sections_found": 1}, {"record_id": 224, "company": "LSEG", "year": 2021, "file_name": "Pages from LSEG_Annual_2021.pdf", "issues": ["KAM title not found in PDF"], "confidence_score": 70, "kam_sections_found": 1}, {"record_id": 225, "company": "LSEG", "year": 2021, "file_name": "Pages from LSEG_Annual_2021.pdf", "issues": [], "confidence_score": 100, "kam_sections_found": 1}, {"record_id": 226, "company": "LSEG", "year": 2021, "file_name": "Pages from LSEG_Annual_2021.pdf", "issues": ["KAM title not found in PDF"], "confidence_score": 70, "kam_sections_found": 1}, {"record_id": 227, "company": "LSEG", "year": 2021, "file_name": "Pages from LSEG_Annual_2021.pdf", "issues": ["KAM title not found in PDF"], "confidence_score": 70, "kam_sections_found": 1}, {"record_id": 228, "company": "LSEG", "year": 2021, "file_name": "Pages from LSEG_Annual_2021.pdf", "issues": ["KAM title not found in PDF"], "confidence_score": 70, "kam_sections_found": 1}, {"record_id": 229, "company": "LSEG", "year": 2021, "file_name": "Pages from LSEG_Annual_2021.pdf", "issues": ["KAM title not found in PDF"], "confidence_score": 70, "kam_sections_found": 1}, {"record_id": 230, "company": "LSEG", "year": 2021, "file_name": "Pages from LSEG_Annual_2023.pdf", "issues": ["KAM title not found in PDF"], "confidence_score": 70, "kam_sections_found": 1}, {"record_id": 231, "company": "LSEG", "year": 2022, "file_name": "Pages from LSEG_Annual_2022.pdf", "issues": [], "confidence_score": 100, "kam_sections_found": 1}, {"record_id": 232, "company": "LSEG", "year": 2022, "file_name": "Pages from LSEG_Annual_2022.pdf", "issues": [], "confidence_score": 100, "kam_sections_found": 1}, {"record_id": 233, "company": "LSEG", "year": 2022, "file_name": "Pages from LSEG_Annual_2022.pdf", "issues": ["KAM title not found in PDF"], "confidence_score": 70, "kam_sections_found": 1}, {"record_id": 234, "company": "LSEG", "year": 2022, "file_name": "Pages from LSEG_Annual_2022.pdf", "issues": ["KAM title not found in PDF", "Auditor mismatch: PDF=Ernst & Young, Record=[具体审计师名称]"], "confidence_score": 45, "kam_sections_found": 1}, {"record_id": 235, "company": "LSEG", "year": 2022, "file_name": "Pages from LSEG_Annual_2022.pdf", "issues": ["KAM title not found in PDF"], "confidence_score": 70, "kam_sections_found": 1}, {"record_id": 236, "company": "LSEG", "year": 2022, "file_name": "Pages from LSEG_Annual_2022.pdf", "issues": ["KAM title not found in PDF"], "confidence_score": 70, "kam_sections_found": 1}, {"record_id": 237, "company": "LSEG", "year": 2022, "file_name": "Pages from LSEG_Annual_2022.pdf", "issues": ["KAM title not found in PDF"], "confidence_score": 70, "kam_sections_found": 1}, {"record_id": 238, "company": "LSEG", "year": 2022, "file_name": "Pages from LSEG_Annual_2022.pdf", "issues": ["Auditor mismatch: PDF=Ernst & Young, Record=[具体审计师名称]"], "confidence_score": 75, "kam_sections_found": 1}, {"record_id": 239, "company": "LSEG", "year": 2022, "file_name": "Pages from LSEG_Annual_2022.pdf", "issues": [], "confidence_score": 100, "kam_sections_found": 1}, {"record_id": 240, "company": "LSEG", "year": 2022, "file_name": "Pages from LSEG_Annual_2022.pdf", "issues": [], "confidence_score": 100, "kam_sections_found": 1}, {"record_id": 241, "company": "LSEG", "year": 2023, "file_name": "Pages from LSEG_Annual_2023.pdf", "issues": [], "confidence_score": 100, "kam_sections_found": 1}, {"record_id": 242, "company": "LSEG", "year": 2023, "file_name": "Pages from LSEG_Annual_2023.pdf", "issues": ["Auditor mismatch: PDF=Ernst & Young, Record=Unknown"], "confidence_score": 75, "kam_sections_found": 1}, {"record_id": 243, "company": "LSEG", "year": 2023, "file_name": "Pages from LSEG_Annual_2023.pdf", "issues": ["KAM title not found in PDF"], "confidence_score": 70, "kam_sections_found": 1}, {"record_id": 244, "company": "LSEG", "year": 2023, "file_name": "Pages from LSEG_Annual_2023.pdf", "issues": ["KAM title not found in PDF", "Auditor mismatch: PDF=Ernst & Young, Record=Unknown"], "confidence_score": 45, "kam_sections_found": 1}, {"record_id": 245, "company": "LSEG", "year": 2023, "file_name": "Pages from LSEG_Annual_2023.pdf", "issues": ["KAM title not found in PDF"], "confidence_score": 70, "kam_sections_found": 1}, {"record_id": 246, "company": "LSEG", "year": 2023, "file_name": "Pages from LSEG_Annual_2023.pdf", "issues": ["KAM title not found in PDF", "Auditor mismatch: PDF=Ernst & Young, Record=Unknown"], "confidence_score": 45, "kam_sections_found": 1}, {"record_id": 247, "company": "LSEG", "year": 2023, "file_name": "Pages from LSEG_Annual_2023.pdf", "issues": [], "confidence_score": 100, "kam_sections_found": 1}, {"record_id": 248, "company": "LSEG", "year": 2023, "file_name": "Pages from LSEG_Annual_2023.pdf", "issues": ["Auditor mismatch: PDF=Ernst & Young, Record=Unknown"], "confidence_score": 75, "kam_sections_found": 1}, {"record_id": 249, "company": "LSEG", "year": 2024, "file_name": "Pages from LSEG_Annual_2024.pdf", "issues": [], "confidence_score": 100, "kam_sections_found": 1}, {"record_id": 250, "company": "LSEG", "year": 2024, "file_name": "Pages from LSEG_Annual_2024.pdf", "issues": ["Auditor mismatch: PDF=Deloitte, Record=Unknown"], "confidence_score": 75, "kam_sections_found": 1}, {"record_id": 251, "company": "LSEG", "year": 2024, "file_name": "Pages from LSEG_Annual_2024.pdf", "issues": ["Auditor mismatch: PDF=Deloitte, Record=Independent Auditor"], "confidence_score": 75, "kam_sections_found": 1}, {"record_id": 252, "company": "LSEG", "year": 2024, "file_name": "Pages from LSEG_Annual_2024.pdf", "issues": [], "confidence_score": 100, "kam_sections_found": 1}, {"record_id": 253, "company": "LSEG", "year": 2024, "file_name": "Pages from LSEG_Annual_2024.pdf", "issues": ["Auditor mismatch: PDF=Deloitte, Record=Unknown"], "confidence_score": 75, "kam_sections_found": 1}, {"record_id": 254, "company": "LSEG", "year": 2024, "file_name": "Pages from LSEG_Annual_2024.pdf", "issues": ["Auditor mismatch: PDF=Deloitte, Record=Independent Auditor"], "confidence_score": 75, "kam_sections_found": 1}, {"record_id": 255, "company": "LSEG", "year": 2024, "file_name": "Pages from LSEG_Annual_2024.pdf", "issues": ["KAM title not found in PDF"], "confidence_score": 70, "kam_sections_found": 1}, {"record_id": 256, "company": "LSEG", "year": 2024, "file_name": "Pages from LSEG_Annual_2024.pdf", "issues": ["Auditor mismatch: PDF=Deloitte, Record=Unknown"], "confidence_score": 75, "kam_sections_found": 1}, {"record_id": 257, "company": "LSEG", "year": 2024, "file_name": "Pages from LSEG_Annual_2024.pdf", "issues": ["Auditor mismatch: PDF=Deloitte, Record=Independent Auditor"], "confidence_score": 75, "kam_sections_found": 1}, {"record_id": 258, "company": "Nasdaq Inc.", "year": 2015, "file_name": "Pages from NASDAQ_Annual_2015.pdf", "issues": [], "confidence_score": 45, "kam_sections_found": 1}, {"record_id": 259, "company": "Nasdaq Inc.", "year": 2016, "file_name": "Pages from NASDAQ_Annual_2016.pdf", "issues": [], "confidence_score": 45, "kam_sections_found": 1}, {"record_id": 260, "company": "Nasdaq Inc.", "year": 2017, "file_name": "Pages from NASDAQ_Annual_2017.pdf", "issues": [], "confidence_score": 45, "kam_sections_found": 1}, {"record_id": 261, "company": "Nasdaq Inc.", "year": 2018, "file_name": "Pages from NASDAQ_Annual_2018.pdf", "issues": [], "confidence_score": 45, "kam_sections_found": 1}, {"record_id": 262, "company": "Nasdaq Inc.", "year": 2019, "file_name": "Pages from NASDAQ_Annual_2019.pdf", "issues": ["Could not extract text from PDF"], "confidence_score": 0}, {"record_id": 263, "company": "Nasdaq Inc.", "year": 2019, "file_name": "Pages from NASDAQ_Annual_2019.pdf", "issues": ["Could not extract text from PDF"], "confidence_score": 0}, {"record_id": 264, "company": "Nasdaq Inc.", "year": 2020, "file_name": "Pages from NASDAQ_Annual_2020.pdf", "issues": ["Auditor mismatch: PDF=Ernst & Young, Record=Independent Registered Public Accounting Firm"], "confidence_score": 75, "kam_sections_found": 1}, {"record_id": 265, "company": "Nasdaq Inc.", "year": 2020, "file_name": "Pages from NASDAQ_Annual_2020.pdf", "issues": [], "confidence_score": 100, "kam_sections_found": 1}, {"record_id": 266, "company": "Nasdaq Inc.", "year": 2021, "file_name": "Pages from NASDAQ_Annual_2021.pdf", "issues": [], "confidence_score": 100, "kam_sections_found": 1}, {"record_id": 267, "company": "Nasdaq Inc.", "year": 2021, "file_name": "Pages from NASDAQ_Annual_2021.pdf", "issues": ["KAM title not found in PDF", "Auditor mismatch: PDF=Ernst & Young, Record=Independent Registered Public Accounting Firm"], "confidence_score": 45, "kam_sections_found": 1}, {"record_id": 268, "company": "Nasdaq Inc.", "year": 2021, "file_name": "Pages from NASDAQ_Annual_2021.pdf", "issues": ["KAM title not found in PDF", "Auditor mismatch: PDF=Ernst & Young, Record=Independent Registered Public Accounting Firm"], "confidence_score": 45, "kam_sections_found": 1}, {"record_id": 269, "company": "Nasdaq Inc.", "year": 2021, "file_name": "Pages from NASDAQ_Annual_2021.pdf", "issues": [], "confidence_score": 100, "kam_sections_found": 1}, {"record_id": 270, "company": "Nasdaq Inc.", "year": 2022, "file_name": "Pages from NASDAQ_Annual_2022.pdf", "issues": [], "confidence_score": 100, "kam_sections_found": 1}, {"record_id": 271, "company": "Nasdaq Inc.", "year": 2022, "file_name": "Pages from NASDAQ_Annual_2022.pdf", "issues": ["KAM title not found in PDF", "Auditor mismatch: PDF=Ernst & Young, Record=Independent Registered Public Accounting Firm"], "confidence_score": 45, "kam_sections_found": 1}, {"record_id": 272, "company": "Nasdaq Inc.", "year": 2023, "file_name": "Pages from NASDAQ_Annual_2023.pdf", "issues": [], "confidence_score": 100, "kam_sections_found": 1}, {"record_id": 273, "company": "Nasdaq Inc.", "year": 2023, "file_name": "Pages from NASDAQ_Annual_2023.pdf", "issues": ["KAM title not found in PDF", "Auditor mismatch: PDF=Ernst & Young, Record=Independent Registered Public Accounting Firm"], "confidence_score": 45, "kam_sections_found": 1}, {"record_id": 274, "company": "TMX Group", "year": 2015, "file_name": "Pages from TMX_Annual_2015.pdf", "issues": [], "confidence_score": 45, "kam_sections_found": 1}, {"record_id": 275, "company": "TMX Group", "year": 2015, "file_name": "Pages from TMX_Annual_2016.pdf", "issues": [], "confidence_score": 45, "kam_sections_found": 1}, {"record_id": 276, "company": "TMX Group", "year": 2016, "file_name": "Pages from TMX_Annual_2016.pdf", "issues": [], "confidence_score": 45, "kam_sections_found": 1}, {"record_id": 277, "company": "TMX Group", "year": 2017, "file_name": "Pages from TMX_Annual_2017.pdf", "issues": [], "confidence_score": 45, "kam_sections_found": 1}, {"record_id": 278, "company": "TMX Group", "year": 2018, "file_name": "Pages from TMX_Annual_2018.pdf", "issues": ["KAM title not found in PDF"], "confidence_score": 70, "kam_sections_found": 2}, {"record_id": 279, "company": "TMX Group", "year": 2018, "file_name": "Pages from TMX_Annual_2018.pdf", "issues": ["KAM title not found in PDF"], "confidence_score": 70, "kam_sections_found": 2}, {"record_id": 280, "company": "TMX Group", "year": 2019, "file_name": "Pages from TMX_Annual_2019.pdf", "issues": ["KAM title not found in PDF"], "confidence_score": 70, "kam_sections_found": 2}, {"record_id": 281, "company": "TMX Group", "year": 2019, "file_name": "Pages from TMX_Annual_2019.pdf", "issues": ["KAM title not found in PDF"], "confidence_score": 70, "kam_sections_found": 2}, {"record_id": 282, "company": "TMX Group", "year": 2020, "file_name": "Pages from TMX_Annual_2020.pdf", "issues": ["KAM title not found in PDF"], "confidence_score": 70, "kam_sections_found": 1}, {"record_id": 283, "company": "TMX Group", "year": 2021, "file_name": "Pages from TMX_Annual_2021.pdf", "issues": ["KAM title not found in PDF", "Auditor mismatch: PDF=KPMG, Record=Unknown"], "confidence_score": 45, "kam_sections_found": 1}, {"record_id": 284, "company": "TMX Group", "year": 2022, "file_name": "Pages from TMX_Annual_2022.pdf", "issues": ["KAM title not found in PDF"], "confidence_score": 70, "kam_sections_found": 5}, {"record_id": 285, "company": "TMX Group", "year": 2022, "file_name": "Pages from TMX_Annual_2021.pdf", "issues": ["KAM title not found in PDF", "Auditor mismatch: PDF=KPMG, Record=NDmna tChartered Professional Accountants, Licensed Public Accountants"], "confidence_score": 45, "kam_sections_found": 1}, {"record_id": 286, "company": "TMX Group", "year": 2022, "file_name": "Pages from TMX_Annual_2022.pdf", "issues": ["KAM title not found in PDF", "Auditor mismatch: PDF=KPMG, Record=Abhimanyu Verma"], "confidence_score": 45, "kam_sections_found": 5}, {"record_id": 287, "company": "TMX Group", "year": 2023, "file_name": "Pages from TMX_Annual_2023.pdf", "issues": ["KAM title not found in PDF"], "confidence_score": 70, "kam_sections_found": 1}, {"record_id": 288, "company": "TMX Group", "year": 2024, "file_name": "Pages from TMX_Annual_2024.pdf", "issues": ["KAM title not found in PDF"], "confidence_score": 70, "kam_sections_found": 1}, {"record_id": 289, "company": "TMX Group", "year": 2024, "file_name": "Pages from TMX_Annual_2024.pdf", "issues": ["KAM title not found in PDF"], "confidence_score": 70, "kam_sections_found": 1}]}