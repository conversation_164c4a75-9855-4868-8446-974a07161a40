<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>年度報告庫演示 - 全球交易所審計分析</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
    <style>
        .demo-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 3rem 0;
            text-align: center;
        }
        
        .demo-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }
        
        .demo-subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto;
        }
        
        .demo-stats {
            display: flex;
            justify-content: center;
            gap: 3rem;
            margin-top: 2rem;
        }
        
        .demo-stat {
            text-align: center;
        }
        
        .demo-stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: #fbbf24;
        }
        
        .demo-stat-label {
            font-size: 0.9rem;
            opacity: 0.8;
        }
        
        .back-to-main {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.9);
            color: #2563eb;
            padding: 0.75rem 1.5rem;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 500;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            z-index: 1000;
        }
        
        .back-to-main:hover {
            background: white;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
        }
        
        .feature-highlight {
            background: #f8fafc;
            padding: 2rem;
            border-radius: 12px;
            margin: 2rem 0;
            border-left: 4px solid #2563eb;
        }
        
        .feature-highlight h3 {
            color: #1f2937;
            margin-bottom: 1rem;
        }
        
        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }
        
        .feature-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem;
            background: white;
            border-radius: 8px;
            font-size: 0.9rem;
        }
        
        .feature-item i {
            color: #10b981;
            width: 16px;
        }
    </style>
</head>
<body>
    <!-- Back to Main -->
    <a href="index.html" class="back-to-main">
        <i class="fas fa-arrow-left"></i>
        返回主頁
    </a>

    <!-- Demo Header -->
    <section class="demo-header">
        <div class="container">
            <h1 class="demo-title">全球交易所年度報告庫</h1>
            <p class="demo-subtitle">
                完整收錄全球十大股票交易所過去10年的年度報告，支持在線瀏覽、批量下載和數據分析
            </p>
            <div class="demo-stats">
                <div class="demo-stat">
                    <div class="demo-stat-number">10</div>
                    <div class="demo-stat-label">主要交易所</div>
                </div>
                <div class="demo-stat">
                    <div class="demo-stat-number">100+</div>
                    <div class="demo-stat-label">年度報告</div>
                </div>
                <div class="demo-stat">
                    <div class="demo-stat-number">2015-2024</div>
                    <div class="demo-stat-label">時間範圍</div>
                </div>
                <div class="demo-stat">
                    <div class="demo-stat-number">4</div>
                    <div class="demo-stat-label">大型審計師事務所</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Main Content -->
    <div class="container" style="padding: 2rem 0;">
        <!-- Feature Highlights -->
        <div class="feature-highlight">
            <h3><i class="fas fa-star"></i> 核心功能特色</h3>
            <div class="feature-list">
                <div class="feature-item">
                    <i class="fas fa-link"></i>
                    <span>真實官方鏈接</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-filter"></i>
                    <span>智能篩選和搜索</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-download"></i>
                    <span>批量下載功能</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-chart-bar"></i>
                    <span>實時進度追蹤</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-file-export"></i>
                    <span>數據導出CSV</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-mobile-alt"></i>
                    <span>響應式設計</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-shield-alt"></i>
                    <span>本地狀態管理</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-check-circle"></i>
                    <span>鏈接驗證保證</span>
                </div>
            </div>
        </div>

        <!-- Statistics Section -->
        <div class="feature-highlight">
            <h3><i class="fas fa-chart-bar"></i> 年度報告庫統計</h3>
            <div class="stats-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin: 1rem 0;">
                <div class="stat-card" style="background: white; padding: 1.5rem; border-radius: 8px; text-align: center; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                    <div class="stat-number" data-stat="total" style="font-size: 2rem; font-weight: bold; color: #2563eb;">78</div>
                    <div class="stat-label" style="color: #6b7280; margin-top: 0.5rem;">總報告數</div>
                </div>
                <div class="stat-card" style="background: white; padding: 1.5rem; border-radius: 8px; text-align: center; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                    <div class="stat-number" data-stat="downloaded" style="font-size: 2rem; font-weight: bold; color: #10b981;">78</div>
                    <div class="stat-label" style="color: #6b7280; margin-top: 0.5rem;">已下載</div>
                </div>
                <div class="stat-card" style="background: white; padding: 1.5rem; border-radius: 8px; text-align: center; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                    <div class="stat-number" data-stat="exchanges" style="font-size: 2rem; font-weight: bold; color: #8b5cf6;">8</div>
                    <div class="stat-label" style="color: #6b7280; margin-top: 0.5rem;">交易所</div>
                </div>
                <div class="stat-card" style="background: white; padding: 1.5rem; border-radius: 8px; text-align: center; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                    <div class="stat-number" data-stat="size" style="font-size: 2rem; font-weight: bold; color: #f59e0b;">544MB</div>
                    <div class="stat-label" style="color: #6b7280; margin-top: 0.5rem;">總大小</div>
                </div>
            </div>

            <div class="download-progress" style="margin-top: 1.5rem;">
                <div class="download-status-text" style="text-align: center; margin-bottom: 0.5rem; color: #374151; font-weight: 500;">已下載 78/78 個報告 (100%)</div>
                <div class="progress-bar-container" style="background: #e5e7eb; height: 8px; border-radius: 4px; overflow: hidden;">
                    <div class="download-progress-bar" style="background: linear-gradient(90deg, #10b981, #059669); height: 100%; width: 100%; transition: width 0.3s ease;"></div>
                </div>
            </div>
        </div>

        <!-- Reports Container -->
        <div class="reports-container">
            <div class="reports-header">
                <h3>全球主要交易所年度報告庫 (2015-2024)</h3>
                <div class="reports-controls">
                    <select id="exchangeFilter" onchange="filterReports()">
                        <option value="all">所有交易所</option>
                        <option value="ice">🇺🇸 ICE (NYSE)</option>
                        <option value="nasdaq">🇺🇸 NASDAQ</option>
                        <option value="lseg">🇬🇧 LSEG</option>
                        <option value="hkex">🇭🇰 HKEX</option>
                        <option value="jpx">🇯🇵 JPX</option>
                        <option value="sse">🇨🇳 上海證券交易所</option>
                        <option value="szse">🇨🇳 深圳證券交易所</option>
                        <option value="tmx">🇨🇦 TMX Group</option>
                        <option value="deutsche">🇩🇪 Deutsche Börse</option>
                        <option value="euronext">🇪🇺 Euronext</option>
                    </select>
                    <select id="yearFilter" onchange="filterReports()">
                        <option value="all">所有年份</option>
                        <option value="2024">2024</option>
                        <option value="2023">2023</option>
                        <option value="2022">2022</option>
                        <option value="2021">2021</option>
                        <option value="2020">2020</option>
                        <option value="2019">2019</option>
                        <option value="2018">2018</option>
                        <option value="2017">2017</option>
                        <option value="2016">2016</option>
                        <option value="2015">2015</option>
                    </select>
                    <button class="control-button" onclick="downloadAllReports()">
                        <i class="fas fa-download"></i> 批量下載
                    </button>
                    <button class="control-button" onclick="exportReportsList()">
                        <i class="fas fa-file-export"></i> 導出清單
                    </button>
                </div>
            </div>
            
            <div class="reports-grid" id="reportsGrid">
                <!-- Reports will be populated by JavaScript -->
            </div>
            
            <div class="download-status" id="downloadStatus" style="display: none;">
                <div class="status-header">
                    <h4>下載進度</h4>
                    <button onclick="hideDownloadStatus()" class="close-btn">&times;</button>
                </div>
                <div class="progress-container">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <div class="progress-text" id="progressText">準備中...</div>
                </div>
                <div class="download-log" id="downloadLog"></div>
            </div>
        </div>

        <!-- Usage Instructions -->
        <div class="feature-highlight" style="margin-top: 3rem;">
            <h3><i class="fas fa-info-circle"></i> 使用說明</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem; margin-top: 1rem;">
                <div>
                    <h4 style="color: #2563eb; margin-bottom: 0.5rem;">1. 瀏覽報告</h4>
                    <p style="font-size: 0.9rem; color: #6b7280; margin: 0;">使用頂部篩選器選擇特定交易所和年份，瀏覽報告卡片查看詳細信息。</p>
                </div>
                <div>
                    <h4 style="color: #2563eb; margin-bottom: 0.5rem;">2. 在線查看</h4>
                    <p style="font-size: 0.9rem; color: #6b7280; margin: 0;">點擊"在線查看"按鈕直接訪問交易所官方報告頁面。</p>
                </div>
                <div>
                    <h4 style="color: #2563eb; margin-bottom: 0.5rem;">3. 下載管理</h4>
                    <p style="font-size: 0.9rem; color: #6b7280; margin: 0;">使用單個下載或批量下載功能，系統會自動追蹤下載狀態。</p>
                </div>
                <div>
                    <h4 style="color: #2563eb; margin-bottom: 0.5rem;">4. 數據導出</h4>
                    <p style="font-size: 0.9rem; color: #6b7280; margin: 0;">導出篩選後的報告清單為CSV格式，便於進一步分析。</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="annual-reports-data.js"></script>
    <script>
        // Annual Reports Functionality (Simplified for demo)
        let allReports = [];
        let filteredReports = [];
        let downloadQueue = [];
        let isDownloading = false;

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', function() {
            loadReportsData();
            renderReports();
        });

        // Load reports data
        function loadReportsData() {
            allReports = [];
            Object.keys(annualReportsData).forEach(exchangeKey => {
                const exchange = annualReportsData[exchangeKey];
                exchange.reports.forEach(report => {
                    allReports.push({
                        ...report,
                        exchangeKey: exchangeKey,
                        exchangeName: exchange.name,
                        region: exchange.region,
                        auditor: exchange.auditor
                    });
                });
            });
            filteredReports = [...allReports];
        }

        // Render reports
        function renderReports() {
            const reportsGrid = document.getElementById('reportsGrid');
            if (!reportsGrid) return;

            reportsGrid.innerHTML = '';

            if (filteredReports.length === 0) {
                reportsGrid.innerHTML = `
                    <div style="grid-column: 1 / -1; text-align: center; padding: 3rem; color: #6b7280;">
                        <i class="fas fa-search" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                        <p>沒有找到符合條件的報告</p>
                    </div>
                `;
                return;
            }

            filteredReports.forEach(report => {
                const reportCard = createReportCard(report);
                reportsGrid.appendChild(reportCard);
            });
        }

        // Create report card
        function createReportCard(report) {
            const card = document.createElement('div');
            card.className = 'report-card';
            
            const isDownloaded = checkIfDownloaded(report.localPath);
            if (isDownloaded) {
                card.classList.add('downloaded');
            }

            card.innerHTML = `
                <div class="report-header">
                    <h4 class="report-title">${report.exchangeName}</h4>
                    <span class="report-year">${report.year}</span>
                </div>
                <div class="report-meta">
                    <div class="report-meta-item">
                        <i class="fas fa-file-pdf"></i>
                        <span>${report.type} • ${report.fileSize} • ${report.pages} 頁</span>
                    </div>
                    <div class="report-meta-item">
                        <i class="fas fa-calendar"></i>
                        <span>發布日期: ${formatDate(report.filingDate)}</span>
                    </div>
                    <div class="report-meta-item">
                        <i class="fas fa-user-tie"></i>
                        <span>審計師: ${report.auditor}</span>
                    </div>
                    <div class="report-meta-item">
                        <i class="fas fa-globe"></i>
                        <span>地區: ${report.region}</span>
                    </div>
                </div>
                <div class="report-actions">
                    <a href="${report.url}" target="_blank" class="report-btn primary">
                        <i class="fas fa-external-link-alt"></i>
                        在線查看
                    </a>
                    <button class="report-btn secondary" onclick="downloadReport('${report.exchangeKey}', ${report.year})">
                        <i class="fas fa-download"></i>
                        ${isDownloaded ? '重新下載' : '下載'}
                    </button>
                    ${isDownloaded ? `
                        <button class="report-btn success" onclick="openLocalReport('${report.localPath}')">
                            <i class="fas fa-folder-open"></i>
                            本地文件
                        </button>
                    ` : ''}
                </div>
            `;

            return card;
        }

        // Filter reports
        function filterReports() {
            const exchangeFilter = document.getElementById('exchangeFilter').value;
            const yearFilter = document.getElementById('yearFilter').value;

            filteredReports = allReports.filter(report => {
                const exchangeMatch = exchangeFilter === 'all' || report.exchangeKey === exchangeFilter;
                const yearMatch = yearFilter === 'all' || report.year.toString() === yearFilter;
                return exchangeMatch && yearMatch;
            });

            renderReports();
        }

        // Download functions (simplified for demo)
        async function downloadReport(exchangeKey, year) {
            const report = allReports.find(r => r.exchangeKey === exchangeKey && r.year === year);
            if (!report) return;

            showDownloadStatus();
            updateDownloadProgress(0, `正在下載 ${report.exchangeName} ${year} 年度報告...`);
            
            // Simulate download
            await simulateDownload(report);
            
            updateDownloadProgress(100, `下載完成: ${report.title}`);
            addDownloadLog(`✓ 成功下載: ${report.title}`, 'success');
            
            setTimeout(() => {
                hideDownloadStatus();
                renderReports();
            }, 1500);
        }

        async function downloadAllReports() {
            if (isDownloading) return;
            
            isDownloading = true;
            downloadQueue = [...filteredReports];
            
            showDownloadStatus();
            updateDownloadProgress(0, `準備下載 ${downloadQueue.length} 個報告...`);
            addDownloadLog(`開始批量下載 ${downloadQueue.length} 個報告`, 'info');
            
            for (let i = 0; i < downloadQueue.length; i++) {
                const report = downloadQueue[i];
                const progress = ((i + 1) / downloadQueue.length) * 100;
                
                updateDownloadProgress(progress, `正在下載 ${report.exchangeName} ${report.year}...`);
                await simulateDownload(report);
                addDownloadLog(`✓ ${report.exchangeName} ${report.year} 下載完成`, 'success');
                
                await new Promise(resolve => setTimeout(resolve, 300));
            }
            
            updateDownloadProgress(100, '所有下載完成！');
            addDownloadLog('批量下載完成', 'success');
            
            setTimeout(() => {
                hideDownloadStatus();
                renderReports();
                isDownloading = false;
            }, 2000);
        }

        // Utility functions
        async function simulateDownload(report) {
            return new Promise(resolve => {
                const sizeInMB = parseFloat(report.fileSize);
                const downloadTime = Math.max(500, sizeInMB * 100);
                setTimeout(() => {
                    markAsDownloaded(report.localPath);
                    resolve();
                }, downloadTime);
            });
        }

        function checkIfDownloaded(localPath) {
            const downloaded = localStorage.getItem('downloadedReports');
            if (!downloaded) return false;
            const downloadedList = JSON.parse(downloaded);
            return downloadedList.includes(localPath);
        }

        function markAsDownloaded(localPath) {
            let downloaded = localStorage.getItem('downloadedReports');
            let downloadedList = downloaded ? JSON.parse(downloaded) : [];
            if (!downloadedList.includes(localPath)) {
                downloadedList.push(localPath);
                localStorage.setItem('downloadedReports', JSON.stringify(downloadedList));
            }
        }

        function openLocalReport(localPath) {
            alert(`本地文件路徑: ${localPath}\n\n注意：這是演示功能。在實際應用中，這裡會打開本地文件。`);
        }

        function exportReportsList() {
            const csvContent = generateReportsCSV();
            downloadFile('annual-reports-list.csv', csvContent, 'text/csv');
        }

        function generateReportsCSV() {
            const headers = ['交易所', '年份', '報告類型', '文件大小', '頁數', '發布日期', '審計師', '地區', '下載鏈接'];
            const rows = [headers.join(',')];
            
            filteredReports.forEach(report => {
                const row = [
                    `"${report.exchangeName}"`,
                    report.year,
                    `"${report.type}"`,
                    `"${report.fileSize}"`,
                    report.pages,
                    report.filingDate,
                    `"${report.auditor}"`,
                    `"${report.region}"`,
                    `"${report.url}"`
                ];
                rows.push(row.join(','));
            });
            
            return rows.join('\n');
        }

        function downloadFile(filename, content, contentType) {
            const blob = new Blob([content], { type: contentType });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
        }

        function showDownloadStatus() {
            document.getElementById('downloadStatus').style.display = 'block';
            document.getElementById('downloadLog').innerHTML = '';
        }

        function hideDownloadStatus() {
            document.getElementById('downloadStatus').style.display = 'none';
        }

        function updateDownloadProgress(percentage, text) {
            document.getElementById('progressFill').style.width = percentage + '%';
            document.getElementById('progressText').textContent = text;
        }

        function addDownloadLog(message, type = 'info') {
            const logDiv = document.getElementById('downloadLog');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('zh-TW', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
        }
    </script>
</body>
</html>
