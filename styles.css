/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: #333;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    padding: 1rem 0;
    transition: all 0.3s ease;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo {
    display: flex;
    align-items: center;
    font-size: 1.5rem;
    font-weight: 700;
    color: #2563eb;
}

.nav-logo i {
    margin-right: 0.5rem;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: color 0.3s ease;
    position: relative;
}

.nav-link:hover {
    color: #2563eb;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: #2563eb;
    transition: width 0.3s ease;
}

.nav-link:hover::after {
    width: 100%;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: #333;
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    position: relative;
    overflow: hidden;
}

.hero-content {
    flex: 1;
    max-width: 600px;
    z-index: 2;
    padding: 0 20px;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.hero-subtitle {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.hero-stats {
    display: flex;
    gap: 2rem;
    margin-bottom: 2rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: #fbbf24;
}

.stat-label {
    font-size: 0.875rem;
    opacity: 0.8;
}

.cta-button {
    background: #fbbf24;
    color: #1f2937;
    border: none;
    padding: 1rem 2rem;
    border-radius: 50px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.cta-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(251, 191, 36, 0.3);
}

.hero-visual {
    flex: 1;
    position: relative;
    height: 500px;
}

.floating-card {
    position: absolute;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    animation: float 6s ease-in-out infinite;
}

.floating-card:nth-child(1) {
    top: 20%;
    right: 20%;
    animation-delay: 0s;
}

.floating-card:nth-child(2) {
    top: 50%;
    right: 10%;
    animation-delay: 2s;
}

.floating-card:nth-child(3) {
    top: 70%;
    right: 30%;
    animation-delay: 4s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

/* Section Styles */
.section {
    padding: 5rem 0;
}

.bg-light {
    background: #f8fafc;
}

.bg-dark {
    background: #1f2937;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    text-align: center;
    margin-bottom: 3rem;
    color: #1f2937;
}

.text-white {
    color: white;
}

/* Overview Section */
.overview-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.overview-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    text-align: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.overview-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.card-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    color: white;
    font-size: 1.5rem;
}

.overview-card h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #1f2937;
}

/* Findings Section */
.findings-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.finding-item {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    cursor: pointer;
}

.finding-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.finding-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #10b981, #059669);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
    color: white;
    font-size: 1.25rem;
}

.finding-item h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #1f2937;
}

.detail-btn {
    background: #2563eb;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-size: 0.875rem;
    cursor: pointer;
    transition: background 0.3s ease;
    margin-top: 1rem;
}

.detail-btn:hover {
    background: #1d4ed8;
}

/* Tab Navigation */
.tab-navigation {
    display: flex;
    justify-content: center;
    margin-bottom: 2rem;
    background: #f1f5f9;
    border-radius: 50px;
    padding: 0.5rem;
}

.tab-btn {
    background: none;
    border: none;
    padding: 1rem 2rem;
    border-radius: 25px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #64748b;
}

.tab-btn.active {
    background: #2563eb;
    color: white;
}

.tab-btn:hover:not(.active) {
    background: #e2e8f0;
}

/* Tab Content */
.tab-content {
    min-height: 400px;
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
    animation: fadeIn 0.5s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.analysis-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
    align-items: start;
    min-height: 400px;
}

.chart-container {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    height: 400px;
    display: flex;
    flex-direction: column;
}

.chart-container h3 {
    margin-bottom: 1rem;
    color: #1f2937;
    flex-shrink: 0;
}

.chart-container canvas {
    flex: 1;
    max-height: 300px !important;
    width: 100% !important;
}

.info-panel {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    height: 400px;
    overflow-y: auto;
}

.metric-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
    border-bottom: 1px solid #e5e7eb;
}

.metric-item:last-child {
    border-bottom: none;
}

.metric-label {
    color: #6b7280;
}

.metric-value {
    font-weight: 600;
    color: #2563eb;
    font-size: 1.1rem;
}

/* Matters List */
.matters-list {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    height: 400px;
    overflow-y: auto;
}

.matter-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1rem 0;
    border-bottom: 1px solid #e5e7eb;
}

.matter-item:last-child {
    border-bottom: none;
}

.matter-rank {
    width: 30px;
    height: 30px;
    background: #2563eb;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    flex-shrink: 0;
}

.matter-content h5 {
    margin-bottom: 0.5rem;
    color: #1f2937;
}

.matter-content p {
    color: #6b7280;
    font-size: 0.875rem;
}

/* Timeline */
.trend-timeline {
    position: relative;
    padding-left: 2rem;
}

.trend-timeline::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #2563eb;
}

.timeline-item {
    position: relative;
    margin-bottom: 2rem;
    background: white;
    padding: 1.5rem;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.timeline-item::before {
    content: '';
    position: absolute;
    left: -1.75rem;
    top: 1.5rem;
    width: 12px;
    height: 12px;
    background: #2563eb;
    border-radius: 50%;
}

.timeline-year {
    font-size: 0.875rem;
    color: #2563eb;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.timeline-content h4 {
    margin-bottom: 0.5rem;
    color: #1f2937;
}

/* Risk Matrix */
.matrix-container {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    margin-top: 2rem;
}

.risk-item {
    padding: 1.5rem;
    border-radius: 10px;
    text-align: center;
    color: white;
    font-weight: 500;
}

.risk-item h4 {
    margin-bottom: 0.5rem;
}

.high-impact.high-probability {
    background: linear-gradient(135deg, #dc2626, #b91c1c);
}

.medium-impact.high-probability {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.high-impact.medium-probability {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.medium-impact.medium-probability {
    background: linear-gradient(135deg, #10b981, #059669);
}

/* Trends Grid */
.trends-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.trend-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    padding: 2rem;
    color: white;
}

.trend-period {
    background: #fbbf24;
    color: #1f2937;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-size: 0.875rem;
    font-weight: 600;
    display: inline-block;
    margin-bottom: 1rem;
}

.trend-card h3 {
    margin-bottom: 1rem;
}

.trend-card ul {
    list-style: none;
}

.trend-card li {
    padding: 0.5rem 0;
    position: relative;
    padding-left: 1.5rem;
}

.trend-card li::before {
    content: '→';
    position: absolute;
    left: 0;
    color: #fbbf24;
    font-weight: bold;
}

/* Stakeholder Tabs */
.stakeholder-tabs {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.stakeholder-btn {
    background: #f1f5f9;
    border: none;
    padding: 1rem 1.5rem;
    border-radius: 10px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #64748b;
}

.stakeholder-btn.active {
    background: #2563eb;
    color: white;
}

.stakeholder-btn:hover:not(.active) {
    background: #e2e8f0;
}

.stakeholder-content {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.stakeholder-pane {
    display: none;
}

.stakeholder-pane.active {
    display: block;
    animation: fadeIn 0.5s ease;
}

.recommendation-list {
    margin-top: 1rem;
}

.recommendation-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 0;
    border-bottom: 1px solid #e5e7eb;
}

.recommendation-item:last-child {
    border-bottom: none;
}

.recommendation-item i {
    color: #10b981;
    font-size: 1.25rem;
}

/* Footer */
.footer {
    background: #1f2937;
    color: white;
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h4 {
    margin-bottom: 1rem;
    color: #fbbf24;
}

.footer-section p {
    margin-bottom: 0.5rem;
    color: #d1d5db;
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid #374151;
    color: #9ca3af;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 2rem;
    border-radius: 15px;
    width: 90%;
    max-width: 600px;
    position: relative;
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from { transform: translateY(-50px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

.close {
    position: absolute;
    right: 1rem;
    top: 1rem;
    font-size: 2rem;
    cursor: pointer;
    color: #6b7280;
    transition: color 0.3s ease;
}

.close:hover {
    color: #1f2937;
}

/* Annual Reports Styles */
.reports-container {
    max-width: 100%;
}

.reports-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.reports-header h3 {
    color: #1f2937;
    margin: 0;
}

.reports-controls {
    display: flex;
    gap: 1rem;
    align-items: center;
    flex-wrap: wrap;
}

.reports-controls select {
    padding: 0.5rem 1rem;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    background: white;
    font-size: 0.875rem;
    min-width: 120px;
}

.reports-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.report-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border-left: 4px solid #2563eb;
}

.report-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.report-card.downloaded {
    border-left-color: #10b981;
    background: #f0fdf4;
}

.report-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.report-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
}

.report-year {
    background: #2563eb;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
}

.report-meta {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 1rem;
    font-size: 0.875rem;
    color: #6b7280;
}

.report-meta-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.report-meta-item i {
    width: 16px;
    color: #9ca3af;
}

.report-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.report-btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 6px;
    font-size: 0.75rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.25rem;
    text-decoration: none;
}

.report-btn.primary {
    background: #2563eb;
    color: white;
}

.report-btn.primary:hover {
    background: #1d4ed8;
}

.report-btn.secondary {
    background: #f3f4f6;
    color: #374151;
    border: 1px solid #d1d5db;
}

.report-btn.secondary:hover {
    background: #e5e7eb;
}

.report-btn.success {
    background: #10b981;
    color: white;
}

.report-btn.disabled {
    background: #9ca3af;
    color: white;
    cursor: not-allowed;
}

.download-status {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
    z-index: 2000;
    min-width: 400px;
    max-width: 90vw;
}

.status-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.status-header h4 {
    margin: 0;
    color: #1f2937;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #6b7280;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background 0.3s ease;
}

.close-btn:hover {
    background: #f3f4f6;
}

.progress-container {
    margin-bottom: 1rem;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #e5e7eb;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #2563eb, #10b981);
    width: 0%;
    transition: width 0.3s ease;
}

.progress-text {
    font-size: 0.875rem;
    color: #6b7280;
    text-align: center;
}

.download-log {
    max-height: 200px;
    overflow-y: auto;
    background: #f8fafc;
    border-radius: 6px;
    padding: 1rem;
    font-family: 'Courier New', monospace;
    font-size: 0.75rem;
    color: #374151;
    border: 1px solid #e5e7eb;
}

.log-entry {
    margin-bottom: 0.25rem;
    padding: 0.25rem 0;
}

.log-entry.success {
    color: #059669;
}

.log-entry.error {
    color: #dc2626;
}

.log-entry.info {
    color: #2563eb;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hamburger {
        display: flex;
    }

    .nav-menu {
        display: none;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-stats {
        flex-direction: column;
        gap: 1rem;
    }

    .analysis-grid {
        grid-template-columns: 1fr;
    }

    .chart-container {
        height: 350px;
    }

    .chart-container canvas {
        max-height: 250px !important;
    }

    .info-panel, .matters-list {
        height: 350px;
    }

    .matrix-container {
        grid-template-columns: 1fr;
    }

    .stakeholder-tabs {
        flex-direction: column;
    }

    .tab-navigation {
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .tab-btn {
        padding: 0.75rem 1rem;
        font-size: 0.875rem;
    }

    .reports-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .reports-controls {
        width: 100%;
        justify-content: space-between;
    }

    .reports-grid {
        grid-template-columns: 1fr;
    }

    .download-status {
        min-width: 300px;
        padding: 1.5rem;
    }
}

@media (max-width: 480px) {
    .chart-container {
        height: 300px;
        padding: 1rem;
    }

    .chart-container canvas {
        max-height: 200px !important;
    }

    .info-panel, .matters-list {
        height: 300px;
        padding: 1rem;
    }

    .hero-title {
        font-size: 2rem;
    }
}

/* File Access Modal */
.file-access-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    position: relative;
    background: white;
    border-radius: 12px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    z-index: 1001;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    border-bottom: 1px solid #eee;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px 12px 0 0;
}

.modal-header h3 {
    margin: 0;
    font-size: 1.2rem;
    display: flex;
    align-items: center;
    gap: 10px;
}

.close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 5px;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.close-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

.modal-body {
    padding: 25px;
}

.file-path {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    margin: 10px 0 20px 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.file-path code {
    flex: 1;
    background: none;
    border: none;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.9rem;
    color: #495057;
    word-break: break-all;
}

.copy-btn, .file-btn {
    background: #007bff;
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.85rem;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    gap: 5px;
}

.copy-btn:hover, .file-btn:hover {
    background: #0056b3;
    transform: translateY(-1px);
}

.access-options {
    margin-top: 20px;
}

.access-options h4 {
    color: #333;
    margin-bottom: 15px;
    font-size: 1.1rem;
}

.option {
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #007bff;
}

.option strong {
    color: #333;
    display: block;
    margin-bottom: 8px;
}

.command-box {
    background: #2d3748;
    color: #e2e8f0;
    padding: 12px;
    border-radius: 6px;
    margin-top: 8px;
    display: flex;
    align-items: center;
    gap: 10px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.command-box code {
    flex: 1;
    background: none;
    color: #e2e8f0;
    font-size: 0.9rem;
}

/* Toast Notifications */
.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    padding: 15px 20px;
    display: flex;
    align-items: center;
    gap: 10px;
    z-index: 1002;
    transform: translateX(400px);
    opacity: 0;
    transition: all 0.3s ease;
    border-left: 4px solid #007bff;
}

.toast.show {
    transform: translateX(0);
    opacity: 1;
}

.toast-success {
    border-left-color: #28a745;
    color: #155724;
}

.toast-error {
    border-left-color: #dc3545;
    color: #721c24;
}

.toast-info {
    border-left-color: #17a2b8;
    color: #0c5460;
}
